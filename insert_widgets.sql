-- Insert widgets into mo_widgets table
-- Table structure: id, index, name, widget, type, subType, filters, slug, call_api, widget_category, organization_id, created_by, updated_by, createdAt, updatedAt

INSERT INTO mo_widgets (`index`, `name`, `widget`, `type`, `subType`, `filters`, `slug`, `call_api`, `widget_category`, `organization_id`, `created_by`, `updated_by`, `createdAt`, `updatedAt`) VALUES
(1, 'User Number Card (total , pending processs, terminal, in probation, etc)', 'user', 'counts', NULL, NULL, 'user_count', 0, 'hr', NULL, 3, 3, NOW(), NOW()),
(2, 'Total Departments & Branch', 'user', 'counts', NULL, NULL, 'dept_branch_count', 0, 'hr', NULL, 3, 3, NOW(), NOW()),
(3, 'Employee on Leave Today & Requested Leave', 'user', 'counts', NULL, NULL, 'leave_today_requested_count', 0, 'hr', NULL, 3, 3, NOW(), NOW()),
(4, 'Change Request Number Card', 'user', 'counts', NULL, NULL, 'change_requests_count', 0, 'hr', NULL, 3, 3, NOW(), NOW()),
(5, 'Resignation Number Card', 'user', 'counts', NULL, NULL, 'resignations_count', 0, 'hr', NULL, 3, 3, NOW(), NOW()),
(6, 'Branch Count Simple Number Card', 'branch', 'counts', NULL, NULL, 'active_branch_dept_count', 0, 'hr', NULL, 3, 3, NOW(), NOW()),
(7, 'Onboarding Pipeline Status', 'user', 'charts', 'pie', '[{\"label\":\"Current Month\",\"value\":\"month\"}]', 'onboarding_pie_chart', 0, 'hr', NULL, 3, 3, NOW(), NOW()),
(8, 'Onboarding Pipeline Status', 'user', 'charts', 'bar', NULL, 'onboarding_bar_chart', 0, 'hr', NULL, 3, 3, NOW(), NOW()),
(9, 'User Contract Graph', 'user', 'charts', 'meter', NULL, 'user_contracts_meter_chart', 0, 'hr', NULL, 3, 3, NOW(), NOW()),
(10, 'Leave Comparison Branch-wise', 'user', 'charts', 'line', NULL, 'leave_comparison_line_chart', 0, 'hr', NULL, 3, 3, NOW(), NOW()),
(11, 'User Activity', 'static', 'activity_list', NULL, NULL, 'user_activity_log_list', 1, 'hr', NULL, 3, 3, NOW(), NOW()),
(12, 'Notification Center', 'static', 'notification_list', NULL, '[\"read\",\"pending\"]', 'notification_list', 1, 'hr', NULL, 3, 3, NOW(), NOW()),
(13, 'Rota shift count', 'user', 'counts', NULL, NULL, 'rota_shift_count', 0, 'hr', NULL, 3, 3, NOW(), NOW()),
(14, 'Document Training graph', 'user', 'charts', 'progress_bar', NULL, 'training_progress_chart', 0, 'hr', NULL, 3, 3, NOW(), NOW()),
(15, 'Daily sales report /Weekly sales report / Expense report', 'sales', 'charts', 'multi_line', "[  { label: ""this week"", value: ""this_week"" },{ label: ""this year"", value: ""this_year"" } 
{ label: ""this month"", value: ""this_month"" } 
 { label: ""last month"", value: ""last_month"" } 
 { label: ""last week"", value: ""last_week"" } 
 { label: ""last year"", value: ""last_year"" }  ] ", 'logbook_line_chart', 0, 'sales', NULL, 3, 3, NOW(), NOW()),
(18, 'Dsr request list', 'static', 'request_list', NULL, NULL, 'dsr_request_list', 1, 'sales', NULL, 3, 3, NOW(), NOW()),
(19, 'WSR request', 'static', 'request_list', NULL, NULL, 'wsr_request_list', 1, 'sales', NULL, 3, 3, NOW(), NOW()),
(20, 'Expense request', 'static', 'request_list', NULL, NULL, 'expense_request_list', 1, 'sales', NULL, 3, 3, NOW(), NOW()),
(21, 'Notification list', 'static', 'notification_list', NULL, NULL, 'sale_notification_list', 1, 'sales', NULL, 3, 3, NOW(), NOW()),
(22, 'Budget and forcasting & target', 'sales', 'charts', 'meter', 'year', 'budget_meter_chart', 0, 'sales', NULL, 3, 3, NOW(), NOW()),
(23, 'Budget and forcasting & target', 'sales', 'charts', 'area', 'year', 'budget_combo_chart', 0, 'sales', NULL, 3, 3, NOW(), NOW()),
(24, 'Budget and forcasting & target', 'sales', 'charts', 'line', '[{\"label\":\"this week\",\"value\":\"this_week\"},{\"label\":\"this year\",\"value\":\"this_year\"},{\"label\":\"this month\",\"value\":\"this_month\"},{\"label\":\"last month\",\"value\":\"last_month\"},{\"label\":\"last week\",\"value\":\"last_week\"},{\"label\":\"last year\",\"value\":\"last_year\"}]', 'budget_line_chart', 0, 'sales', NULL, 3, 3, NOW(), NOW()),
(25, 'Number of documents', 'user', 'counts', NULL, NULL, 'doc_count', 0, 'admin', NULL, 3, 3, NOW(), NOW()),
(26, 'Number of contract', 'user', 'counts', NULL, NULL, 'contract_count', 0, 'admin', NULL, 3, 3, NOW(), NOW()),
(27, 'Roles', 'user', 'counts', NULL, NULL, 'role_count', 0, 'admin', NULL, 3, 3, NOW(), NOW()),
(28, 'Plan detail', 'static', 'plan_detail', NULL, NULL, 'plan_details', 1, 'admin', NULL, 3, 3, NOW(), NOW()),
(29, 'Storage usage', 'static', 'storage_usage', NULL, NULL, 'storage_usage', 1, 'admin', NULL, 3, 3, NOW(), NOW()),
(30, 'Leave type and leave policy count', 'static', 'leave_policy', NULL, NULL, 'leave_policy_count', 1, 'self', NULL, 3, 3, NOW(), NOW()),
(31, 'Recipes', 'user', 'counts', NULL, NULL, 'recipe_count', 0, 'admin', NULL, 3, 3, NOW(), NOW()),
(32, 'Total Holidays', 'static', 'holiday_list', NULL, NULL, 'holiday_list', 1, 'admin', NULL, 3, 3, NOW(), NOW()),
(33, 'User Activity', 'static', 'activity_list', NULL, NULL, 'user_activity_log_list', 1, 'admin', NULL, 3, 3, NOW(), NOW()),
(34, 'Notification Center', 'static', 'notification_list', NULL, NULL, 'notification_list', 1, 'admin', NULL, 3, 3, NOW(), NOW()),
(35, 'attandance report card', 'user', 'counts', NULL, NULL, 'attandance_report_card', 0, 'admin', NULL, 3, 3, NOW(), NOW()),
(36, 'my attandance card', 'user', 'counts', NULL, NULL, 'my_attanadance_card', 0, 'self', NULL, 3, 3, NOW(), NOW());

(16, 16, 'assignment to specific users', 'user', 'assignment_details', NULL, '[ { label: "Past month", value: "past_month" },{ label: "Current month", value: "current_month" }]', 'assignment_card', 0, 'self', NULL, 3, 3, NOW(), NOW()),

(17, 17, 'Recipe assignment details to users', 'user', 'recipe_assignment_details', NULL, NULL, 'recipe_assignment_details', 0, 'self', NULL, 3, 3, NOW(), NOW());

Recipe assignment details to users	user	recipe_assignment_details				self