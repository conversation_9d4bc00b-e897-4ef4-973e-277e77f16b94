import { 
  addNotificationPreferences, 
  clearNotificationPreferencesCache, 
  getNotificationCacheStats 
} from "../helper/common";

// Test data simulating real templateData structures from the codebase
const testTemplateDataSamples = [
  // Pattern 1: Direct organization_id
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    mail_type: "leave_request",
    organization_id: "org_123",
    ORGANIZATION_LOGO: "https://api.example.com/uploads/org_123/logo.png"
  },
  
  // Pattern 2: Organization ID in logo URL only
  {
    name: "<PERSON>", 
    email: "<EMAIL>",
    mail_type: "onboarding_expiring",
    ORGANIZATION_LOGO: "https://api.example.com/uploads/org_456/logo.png"
  },
  
  // Pattern 3: Email lookup required
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    mail_type: "probation_expiring_staff",
    LOGO: "https://api.example.com/email_logo/logo.png"
  },
  
  // Pattern 4: Always allowed functionality
  {
    name: "<PERSON>",
    email: "<EMAIL>", 
    mail_type: "user_verification",
    organization_id: "org_789"
  },
  
  // Pattern 5: Multiple emails (comma-separated)
  {
    name: "Team Notification",
    email: "<EMAIL>,<EMAIL>,<EMAIL>",
    mail_type: "general_notification",
    organization_id: "org_999"
  }
];

export const testNotificationPerformance = async () => {
  console.log('🚀 Starting Notification Performance Test...\n');

  // Clear cache to start fresh
  clearNotificationPreferencesCache();

  // Test 1: Cold cache performance (first run)
  console.log('📊 Test 1: Cold Cache Performance');
  const coldStartTime = Date.now();
  
  for (let i = 0; i < testTemplateDataSamples.length; i++) {
    const sample = testTemplateDataSamples[i];
    const startTime = Date.now();
    
    const result = await addNotificationPreferences(sample);
    const endTime = Date.now();
    
    console.log(`  Sample ${i + 1}: ${endTime - startTime}ms - ${sample.mail_type} (${result.allowed.email ? 'E' : ''}${result.allowed.push ? 'P' : ''}${result.allowed.banner ? 'B' : ''})`);
  }
  
  const coldTotalTime = Date.now() - coldStartTime;
  console.log(`  ⏱️  Cold cache total: ${coldTotalTime}ms\n`);

  // Test 2: Warm cache performance (repeat same requests)
  console.log('📊 Test 2: Warm Cache Performance');
  const warmStartTime = Date.now();
  
  for (let i = 0; i < testTemplateDataSamples.length; i++) {
    const sample = testTemplateDataSamples[i];
    const startTime = Date.now();
    
    const result = await addNotificationPreferences(sample);
    const endTime = Date.now();
    
    console.log(`  Sample ${i + 1}: ${endTime - startTime}ms - ${sample.mail_type} (cached)`);
  }
  
  const warmTotalTime = Date.now() - warmStartTime;
  console.log(`  ⏱️  Warm cache total: ${warmTotalTime}ms\n`);

  // Test 3: Bulk processing simulation
  console.log('📊 Test 3: Bulk Processing Simulation (100 notifications)');
  const bulkStartTime = Date.now();
  
  const bulkPromises = [];
  for (let i = 0; i < 100; i++) {
    const sampleIndex = i % testTemplateDataSamples.length;
    const sample = { 
      ...testTemplateDataSamples[sampleIndex],
      email: `user${i}@example.com` // Vary emails to test different scenarios
    };
    bulkPromises.push(addNotificationPreferences(sample));
  }
  
  await Promise.all(bulkPromises);
  const bulkTotalTime = Date.now() - bulkStartTime;
  console.log(`  ⏱️  Bulk processing (100 notifications): ${bulkTotalTime}ms\n`);

  // Test 4: Cache statistics
  console.log('📊 Test 4: Cache Statistics');
  const cacheStats = getNotificationCacheStats();
  console.log('  Cache Statistics:');
  console.log(`    Preferences Cache: ${cacheStats.preferencesCache.size} entries`);
  console.log(`    Email-Org Cache: ${cacheStats.emailOrgCache.size} entries`);
  console.log('    Cached Keys:', cacheStats.preferencesCache.entries.slice(0, 5), '...\n');

  // Test 5: Performance comparison summary
  console.log('📊 Test 5: Performance Summary');
  const avgColdTime = coldTotalTime / testTemplateDataSamples.length;
  const avgWarmTime = warmTotalTime / testTemplateDataSamples.length;
  const avgBulkTime = bulkTotalTime / 100;
  const speedupRatio = avgColdTime / avgWarmTime;
  
  console.log(`  Average Cold Cache Time: ${avgColdTime.toFixed(2)}ms per notification`);
  console.log(`  Average Warm Cache Time: ${avgWarmTime.toFixed(2)}ms per notification`);
  console.log(`  Average Bulk Time: ${avgBulkTime.toFixed(2)}ms per notification`);
  console.log(`  Cache Speedup Ratio: ${speedupRatio.toFixed(2)}x faster\n`);

  // Test 6: Organization ID extraction methods
  console.log('📊 Test 6: Organization ID Extraction Methods');
  
  const extractionTests = [
    {
      name: "Direct organization_id",
      data: { organization_id: "org_direct" },
      expected: "org_direct"
    },
    {
      name: "Logo URL extraction", 
      data: { ORGANIZATION_LOGO: "https://api.example.com/uploads/org_logo/brand_logo.png" },
      expected: "org_logo"
    },
    {
      name: "Email lookup",
      data: { email: "<EMAIL>" },
      expected: "depends on database"
    }
  ];

  for (const test of extractionTests) {
    const startTime = Date.now();
    const result = await addNotificationPreferences({
      ...test.data,
      mail_type: "test_notification"
    });
    const endTime = Date.now();
    
    console.log(`  ${test.name}: ${endTime - startTime}ms`);
  }

  console.log('\n✅ Performance test completed!');
  
  return {
    coldCacheTime: coldTotalTime,
    warmCacheTime: warmTotalTime,
    bulkProcessingTime: bulkTotalTime,
    speedupRatio: speedupRatio,
    cacheStats: cacheStats
  };
};

// Auto-run the script if executed directly
if (require.main === module) {
  testNotificationPerformance()
    .then((results) => {
      console.log('\n📈 Final Results:', results);
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Performance test failed:', error);
      process.exit(1);
    });
}
