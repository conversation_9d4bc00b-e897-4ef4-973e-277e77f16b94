# Widget API Documentation

This document provides comprehensive information about the Widget Management APIs integrated into the Roles Controller.

## Table of Contents
- [Overview](#overview)
- [Authentication](#authentication)
- [Widget CRUD Operations](#widget-crud-operations)
- [Widget Permission Management](#widget-permission-management)
- [Module Integration](#module-integration)
- [Error Handling](#error-handling)
- [Examples](#examples)

## Overview

The Widget API provides complete CRUD operations for managing widgets within organizations. All widgets are scoped to organizations and include role-based permission management.

**Base URL:** `/api/role`

## Authentication

All widget APIs require authentication. Include the following headers:

```
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
platform-type: web|ios|android
```

## Widget CRUD Operations

### 1. Create Widget

**Endpoint:** `POST /api/role/widgets`

**Description:** Creates a new widget within the authenticated user's organization.

**Request Body:**
```json
{
  "name": "string (required, 1-255 chars)",
  "widget": "string (required)",
  "type": "string (required)",
  "subType": "string (required)", 
  "filters": "array|object|string|null (optional)",
  "slug": "string (required, 1-100 chars, lowercase, alphanumeric with - and _)"
}
```

**Widget Types:**
- `chart` - Chart widgets
- `number` - Number display widgets
- `percentage` - Percentage widgets
- `table` - Table widgets
- `list` - List widgets
- `card` - Card widgets
- `metric` - Metric widgets

**Widget Sub Types:**
- **Chart:** `pie`, `line`, `bar`, `donut`, `area`, `column`, `gauge`
- **Number:** `counter`, `progress`
- **Table:** `simple_table`, `data_table`
- **List:** `bullet_list`, `numbered_list`
- **Card:** `info_card`, `stat_card`
- **Metric:** `kpi`, `summary`

**Example Request:**
```json
{
  "name": "User Statistics Chart",
  "widget": "{\"title\": \"Active Users\", \"dataSource\": \"users\", \"chartType\": \"pie\"}",
  "type": "chart",
  "subType": "pie",
  "filters": [
    {"label": "Last 30 days", "value": "30d"},
    {"label": "Current Month", "value": "month"}
  ],
  "slug": "user_statistics_chart"
}
```

**Response (201 Created):**
```json
{
  "status": true,
  "message": "Widget created successfully",
  "data": {
    "id": 1,
    "index": 1,
    "name": "User Statistics Chart",
    "widget": "{\"title\": \"Active Users\", \"dataSource\": \"users\", \"chartType\": \"pie\"}",
    "type": "chart",
    "subType": "pie",
    "filters": [
      {"label": "Last 30 days", "value": "30d"},
      {"label": "Current Month", "value": "month"}
    ],
    "slug": "user_statistics_chart",
    "organization_id": "org_123",
    "created_by": 1,
    "updated_by": 1,
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

### 2. Get Widgets

**Endpoint:** `GET /api/role/widgets`

**Description:** Retrieves widgets with optional filtering, searching, and role-based permission filtering.

**Query Parameters:**
- `search` (string, optional) - Search in name, slug, type, subType
- `page` (number, optional) - Page number for pagination
- `size` (number, optional, max 100) - Number of items per page
- `id` (number, optional) - Filter by specific widget ID
- `type` (string, optional) - Filter by widget type
- `role_id` (number, optional) - Filter widgets based on role permissions

**Example Request:**
```
GET /api/role/widgets?search=chart&page=1&size=10&role_id=1
```

**Response (200 OK):**
```json
{
  "status": true,
  "message": "Success fetched",
  "data": [
    {
      "id": 1,
      "index": 1,
      "name": "User Statistics Chart",
      "widget": "{\"title\": \"Active Users\", \"dataSource\": \"users\", \"chartType\": \"pie\"}",
      "type": "chart",
      "subType": "pie",
      "filters": [
        {"label": "Last 30 days", "value": "30d"}
      ],
      "slug": "user_statistics_chart",
      "organization_id": "org_123",
      "checked": true,
      "order": 1,
      "created_by": {
        "id": 1,
        "user_first_name": "John",
        "user_last_name": "Doe",
        "user_full_name": "John Doe",
        "user_avatar": "https://api.example.com/avatars/john.jpg"
      },
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  ],
  "totalItems": 1,
  "totalPages": 1,
  "currentPage": 1
}
```

### 3. Get Widget by ID

**Endpoint:** `GET /api/role/widgets/:id`

**Description:** Retrieves a specific widget by its ID.

**Path Parameters:**
- `id` (number, required) - Widget ID

**Example Request:**
```
GET /api/role/widgets/1
```

**Response (200 OK):**
```json
{
  "status": true,
  "message": "Success fetched",
  "data": {
    "id": 1,
    "index": 1,
    "name": "User Statistics Chart",
    "widget": "{\"title\": \"Active Users\", \"dataSource\": \"users\", \"chartType\": \"pie\"}",
    "type": "chart",
    "subType": "pie",
    "filters": [
      {"label": "Last 30 days", "value": "30d"}
    ],
    "slug": "user_statistics_chart",
    "organization_id": "org_123",
    "created_by": {
      "id": 1,
      "user_first_name": "John",
      "user_last_name": "Doe",
      "user_full_name": "John Doe",
      "user_avatar": "https://api.example.com/avatars/john.jpg"
    },
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

### 4. Update Widget

**Endpoint:** `PUT /api/role/widgets/:id`

**Description:** Updates an existing widget. All fields are optional.

**Path Parameters:**
- `id` (number, required) - Widget ID

**Request Body:**
```json
{
  "name": "string (optional)",
  "widget": "string (optional)",
  "type": "string (optional)",
  "subType": "string (optional)",
  "filters": "array|object|string|null (optional)",
  "slug": "string (optional)"
}
```

**Example Request:**
```json
{
  "name": "Updated User Statistics Chart",
  "filters": [
    {"label": "Last 7 days", "value": "7d"},
    {"label": "Last 30 days", "value": "30d"}
  ]
}
```

**Response (200 OK):**
```json
{
  "status": true,
  "message": "Widget updated successfully",
  "data": {
    "id": 1,
    "index": 1,
    "name": "Updated User Statistics Chart",
    "widget": "{\"title\": \"Active Users\", \"dataSource\": \"users\", \"chartType\": \"pie\"}",
    "type": "chart",
    "subType": "pie",
    "filters": [
      {"label": "Last 7 days", "value": "7d"},
      {"label": "Last 30 days", "value": "30d"}
    ],
    "slug": "user_statistics_chart",
    "organization_id": "org_123",
    "created_by": 1,
    "updated_by": 1,
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T11:45:00.000Z"
  }
}
```

### 5. Delete Widget

**Endpoint:** `DELETE /api/role/widgets/:id`

**Description:** Deletes a widget. Checks if widget is in use by any roles before deletion.

**Path Parameters:**
- `id` (number, required) - Widget ID

**Example Request:**
```
DELETE /api/role/widgets/1
```

**Response (200 OK):**
```json
{
  "status": true,
  "message": "Widget deleted successfully"
}
```

**Response (409 Conflict) - Widget in use:**
```json
{
  "status": false,
  "message": "Widget in use cannot delete",
  "data": {
    "permissionsCount": 3,
    "message": "Widget is assigned to roles and cannot be deleted. Remove widget permissions first."
  }
}

## Widget Permission Management

### 1. Update Widget Permissions

**Endpoint:** `PUT /api/role/permissions/widget`

**Description:** Updates widget permissions for a specific role. Supports bulk updates.

**Request Body:**
```json
{
  "role_id": "number (required)",
  "widgets": [
    {
      "widget_id": "number (required)",
      "permission": "number (required, 0-15)",
      "order": "number (optional, default: 0)"
    }
  ]
}
```

**Permission Values:**
- `0` - No access
- `1` - View only
- `3` - View + Create
- `7` - View + Create + Edit
- `15` - Full access (View + Create + Edit + Delete)

**Example Request:**
```json
{
  "role_id": 1,
  "widgets": [
    {
      "widget_id": 1,
      "permission": 15,
      "order": 1
    },
    {
      "widget_id": 2,
      "permission": 1,
      "order": 2
    }
  ]
}
```

**Response (200 OK):**
```json
{
  "status": true,
  "message": "Widget permissions updated successfully",
  "data": {
    "role_id": 1,
    "updated_permissions": 2,
    "widgets": [
      {
        "widget_id": 1,
        "permission": 15,
        "order": 1
      },
      {
        "widget_id": 2,
        "permission": 1,
        "order": 2
      }
    ]
  }
}
```

### 2. Delete Widget Permissions

**Endpoint:** `DELETE /api/role/permissions/widget/:id`

**Description:** Deletes all widget permissions for a specific role.

**Path Parameters:**
- `id` (number, required) - Role ID

**Example Request:**
```
DELETE /api/role/permissions/widget/1
```

**Response (200 OK):**
```json
{
  "status": true,
  "message": "Widget permissions deleted successfully"
}
```

### 3. Copy Widget Permissions

**Endpoint:** `POST /api/role/permissions/widget/copy`

**Description:** Copies widget permissions from one role to multiple target roles.

**Request Body:**
```json
{
  "from_role": "number (required)",
  "to_role": "number|array (required)"
}
```

**Example Request:**
```json
{
  "from_role": 1,
  "to_role": [2, 3, 4]
}
```

**Response (200 OK):**
```json
{
  "status": true,
  "message": "Widget permissions copied successfully",
  "data": {
    "from_role": 1,
    "to_roles": [2, 3, 4],
    "copied_permissions": 5
  }
}
```

### 4. Get Widget Permissions

**Endpoint:** `GET /api/role/permissions?type=widget`

**Description:** Retrieves widget permissions grouped by roles.

**Query Parameters:**
- `type=widget` (required) - Specifies widget permissions
- `search` (string, optional) - Search in widget names, slugs, types
- `page` (number, optional) - Page number
- `size` (number, optional) - Items per page
- `role_id` (number, optional) - Filter by specific role
- `platform` (string, optional) - Filter by platform

**Example Request:**
```
GET /api/role/permissions?type=widget&role_id=1&page=1&size=10
```

**Response (200 OK):**
```json
{
  "status": true,
  "message": "Widget permissions fetched successfully",
  "data": [
    {
      "role_id": 1,
      "role_name": "Admin",
      "platform": "web",
      "additional_permissions": {},
      "created_by": {
        "id": 1,
        "user_first_name": "John",
        "user_last_name": "Doe",
        "user_full_name": "John Doe",
        "user_avatar": "https://api.example.com/avatars/john.jpg"
      },
      "permissions": {
        "user_statistics_chart": {
          "checked": true,
          "permission": 15,
          "order": 1,
          "widget_name": "User Statistics Chart",
          "widget_type": "chart",
          "widget_subType": "pie",
          "widget_slug": "user_statistics_chart",
          "filters": [{"label": "Last 30 days", "value": "30d"}],
          "created_by": {
            "id": 1,
            "user_first_name": "John",
            "user_last_name": "Doe",
            "user_full_name": "John Doe",
            "user_avatar": "https://api.example.com/avatars/john.jpg"
          }
        }
      }
    }
  ],
  "totalItems": 1,
  "totalPages": 1,
  "currentPage": 1
}
```

## Module Integration

### Get Widgets via Modules API

**Endpoint:** `GET /api/role/modules?type=widget`

**Description:** Retrieves widgets through the modules API with role-based filtering.

**Query Parameters:**
- `type=widget` (required) - Returns widgets instead of modules
- `role_id` (number, optional) - Filter widgets by role permissions
- `search` (string, optional) - Search widgets
- `page` (number, optional) - Page number
- `size` (number, optional) - Items per page

**Example Request:**
```
GET /api/role/modules?type=widget&role_id=1&search=chart
```

**Response:** Same format as Get Widgets API

## Error Handling

### Common Error Responses

**400 Bad Request:**
```json
{
  "status": false,
  "message": "Validation error",
  "data": [
    {
      "field": "name",
      "message": "Widget name is required"
    }
  ]
}
```

**403 Forbidden:**
```json
{
  "status": false,
  "message": "Permission denied"
}
```

**404 Not Found:**
```json
{
  "status": false,
  "message": "Widget not found"
}
```

**409 Conflict:**
```json
{
  "status": false,
  "message": "Widget slug already exists"
}
```

**500 Internal Server Error:**
```json
{
  "status": false,
  "message": "Something went wrong",
  "data": "Error details"
}
```

## Examples

### Complete Widget Management Workflow

```javascript
// 1. Create a widget
const createResponse = await fetch('/api/role/widgets', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-token',
    'Content-Type': 'application/json',
    'platform-type': 'web'
  },
  body: JSON.stringify({
    name: 'Sales Dashboard',
    widget: '{"type": "dashboard", "charts": ["sales", "revenue"]}',
    type: 'card',
    subType: 'stat_card',
    filters: [{"period": "monthly"}],
    slug: 'sales_dashboard'
  })
});

// 2. Assign widget permissions to a role
const permissionResponse = await fetch('/api/role/permissions/widget', {
  method: 'PUT',
  headers: {
    'Authorization': 'Bearer your-token',
    'Content-Type': 'application/json',
    'platform-type': 'web'
  },
  body: JSON.stringify({
    role_id: 1,
    widgets: [
      {
        widget_id: 1,
        permission: 15, // Full access
        order: 1
      }
    ]
  })
});

// 3. Get widgets with role filtering
const widgetsResponse = await fetch('/api/role/widgets?role_id=1&page=1&size=10', {
  headers: {
    'Authorization': 'Bearer your-token',
    'platform-type': 'web'
  }
});

// 4. Update widget
const updateResponse = await fetch('/api/role/widgets/1', {
  method: 'PUT',
  headers: {
    'Authorization': 'Bearer your-token',
    'Content-Type': 'application/json',
    'platform-type': 'web'
  },
  body: JSON.stringify({
    name: 'Updated Sales Dashboard',
    filters: [{"period": "weekly"}, {"period": "monthly"}]
  })
});
```

### Widget Types and Use Cases

**Chart Widgets:**
- Use for data visualization
- Support multiple chart types (pie, line, bar, etc.)
- Include filters for data segmentation

**Number Widgets:**
- Display KPIs and metrics
- Support counters and progress indicators
- Good for dashboards

**Table Widgets:**
- Display tabular data
- Support simple and data tables
- Include sorting and filtering

**List Widgets:**
- Show ordered or unordered lists
- Good for navigation menus
- Support bullet and numbered lists

**Card Widgets:**
- Display summary information
- Support info and stat cards
- Good for overview screens

**Metric Widgets:**
- Show business metrics
- Support KPIs and summaries
- Include trend indicators

---

## Notes

- All widgets are organization-scoped
- Widget slugs must be unique within an organization
- The `index` field provides organization-scoped sequential numbering
- Filters are stored as TEXT and automatically parsed to/from JSON
- Widget permissions support granular access control (0-15)
- Platform-specific permissions are supported (web, mobile)
- All APIs include comprehensive validation and error handling
```
