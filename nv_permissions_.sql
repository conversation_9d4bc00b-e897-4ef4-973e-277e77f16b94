-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1deb3
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 04, 2025 at 11:07 AM
-- Server version: 8.0.42-0ubuntu0.24.04.1
-- PHP Version: 8.3.6

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `organization_manager`
--

-- --------------------------------------------------------

--
-- Table structure for table `nv_permissions`
--

CREATE TABLE `nv_permissions` (
  `id` int NOT NULL,
  `role_id` int NOT NULL,
  `module` enum('dashboard','user','branch','department','notification','setting','staff','leave_center','resignation','category','media','playlist','activity_log','branch_card','branch_bank','dsr','dsr_report','change_request','user_invitation','user_verification','employee_contract','forecast','forecast_budget','leave_setting','leave_report','side_letter','setup','rota','recipe') NOT NULL,
  `permission` int NOT NULL,
  `partial` tinyint(1) DEFAULT '0',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `nv_permissions`
--

INSERT INTO `nv_permissions` (`id`, `role_id`, `module`, `permission`, `partial`, `created_by`, `updated_by`, `createdAt`, `updatedAt`) VALUES
(1, 1, 'dashboard', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(2, 1, 'user', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(3, 1, 'branch', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(4, 1, 'department', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(5, 1, 'notification', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(6, 1, 'setting', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(7, 1, 'staff', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(8, 1, 'leave_center', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(9, 1, 'resignation', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(10, 1, 'category', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(11, 1, 'media', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(12, 1, 'playlist', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(13, 1, 'activity_log', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(14, 1, 'branch_card', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(15, 1, 'branch_bank', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(16, 1, 'dsr', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(17, 1, 'dsr_report', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(18, 1, 'change_request', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(19, 1, 'user_invitation', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(20, 1, 'user_verification', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(21, 1, 'employee_contract', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(22, 1, 'forecast', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(23, 1, 'forecast_budget', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(24, 1, 'leave_setting', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(25, 1, 'leave_report', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(26, 1, 'side_letter', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(27, 1, 'setup', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(28, 1, 'rota', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(29, 1, 'recipe', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(30, 2, 'dashboard', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(31, 2, 'user', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(32, 2, 'branch', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(33, 2, 'department', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(34, 2, 'notification', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(35, 2, 'setting', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(36, 2, 'staff', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(37, 2, 'leave_center', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(38, 2, 'resignation', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(39, 2, 'category', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(40, 2, 'media', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(41, 2, 'playlist', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(42, 2, 'activity_log', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(43, 2, 'branch_card', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(44, 2, 'branch_bank', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(45, 2, 'dsr', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(46, 2, 'dsr_report', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(47, 2, 'change_request', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(48, 2, 'user_invitation', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(49, 2, 'user_verification', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(50, 2, 'employee_contract', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(51, 2, 'forecast', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(52, 2, 'forecast_budget', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(53, 2, 'leave_setting', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(54, 2, 'leave_report', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(55, 2, 'side_letter', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(56, 2, 'setup', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(57, 2, 'rota', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(58, 2, 'recipe', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(59, 3, 'dashboard', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(60, 3, 'user', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(61, 3, 'branch', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(62, 3, 'department', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(63, 3, 'notification', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(64, 3, 'setting', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(65, 3, 'staff', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(66, 3, 'leave_center', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(67, 3, 'resignation', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(68, 3, 'category', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(69, 3, 'media', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(70, 3, 'playlist', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(71, 3, 'activity_log', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(72, 3, 'branch_card', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(73, 3, 'branch_bank', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(74, 3, 'dsr', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(75, 3, 'dsr_report', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(76, 3, 'change_request', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(77, 3, 'user_invitation', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(78, 3, 'user_verification', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(79, 3, 'employee_contract', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(80, 3, 'forecast', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(81, 3, 'forecast_budget', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(82, 3, 'leave_setting', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(83, 3, 'leave_report', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(84, 3, 'side_letter', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(85, 3, 'setup', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(86, 3, 'rota', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(87, 3, 'recipe', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(88, 4, 'dashboard', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(89, 4, 'user', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(90, 4, 'branch', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(91, 4, 'department', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(92, 4, 'notification', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(93, 4, 'setting', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(94, 4, 'staff', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(95, 4, 'leave_center', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(96, 4, 'resignation', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(97, 4, 'category', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(98, 4, 'media', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(99, 4, 'playlist', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(100, 4, 'activity_log', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(101, 4, 'branch_card', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(102, 4, 'branch_bank', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(103, 4, 'dsr', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(104, 4, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(105, 4, 'change_request', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(106, 4, 'user_invitation', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(107, 4, 'user_verification', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(108, 4, 'employee_contract', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(109, 4, 'forecast', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(110, 4, 'forecast_budget', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(111, 4, 'leave_setting', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(112, 4, 'leave_report', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(113, 4, 'side_letter', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(114, 4, 'setup', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(115, 4, 'rota', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(116, 4, 'recipe', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(117, 5, 'dashboard', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(118, 5, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(119, 5, 'branch', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(120, 5, 'department', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(121, 5, 'notification', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(122, 5, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(123, 5, 'staff', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(124, 5, 'leave_center', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(125, 5, 'resignation', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(126, 5, 'category', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(127, 5, 'media', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(128, 5, 'playlist', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(129, 5, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(130, 5, 'branch_card', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(131, 5, 'branch_bank', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(132, 5, 'dsr', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(133, 5, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(134, 5, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(135, 5, 'user_invitation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(136, 5, 'user_verification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(137, 5, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(138, 5, 'forecast', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(139, 5, 'forecast_budget', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(140, 5, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(141, 5, 'leave_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(142, 5, 'side_letter', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(143, 5, 'setup', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(144, 5, 'rota', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(145, 5, 'recipe', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(146, 6, 'dashboard', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(147, 6, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(148, 6, 'branch', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(149, 6, 'department', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(150, 6, 'notification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(151, 6, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(152, 6, 'staff', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(153, 6, 'leave_center', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(154, 6, 'resignation', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(155, 6, 'category', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(156, 6, 'media', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(157, 6, 'playlist', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(158, 6, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(159, 6, 'branch_card', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(160, 6, 'branch_bank', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(161, 6, 'dsr', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(162, 6, 'dsr_report', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(163, 6, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(164, 6, 'user_invitation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(165, 6, 'user_verification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(166, 6, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(167, 6, 'forecast', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(168, 6, 'forecast_budget', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(169, 6, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(170, 6, 'leave_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(171, 6, 'side_letter', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(172, 6, 'setup', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(173, 6, 'rota', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(174, 6, 'recipe', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(175, 7, 'dashboard', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(176, 7, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(177, 7, 'branch', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(178, 7, 'department', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(179, 7, 'notification', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(180, 7, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(181, 7, 'staff', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(182, 7, 'leave_center', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(183, 7, 'resignation', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(184, 7, 'category', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(185, 7, 'media', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(186, 7, 'playlist', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(187, 7, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(188, 7, 'branch_card', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(189, 7, 'branch_bank', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(190, 7, 'dsr', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(191, 7, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(192, 7, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(193, 7, 'user_invitation', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(194, 7, 'user_verification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(195, 7, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(196, 7, 'forecast', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(197, 7, 'forecast_budget', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(198, 7, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(199, 7, 'leave_report', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(200, 7, 'side_letter', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(201, 7, 'setup', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(202, 7, 'rota', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(203, 7, 'recipe', 1, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(204, 8, 'dashboard', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(205, 8, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(206, 8, 'branch', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(207, 8, 'department', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(208, 8, 'notification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(209, 8, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(210, 8, 'staff', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(211, 8, 'leave_center', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(212, 8, 'resignation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(213, 8, 'category', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(214, 8, 'media', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(215, 8, 'playlist', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(216, 8, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(217, 8, 'branch_card', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(218, 8, 'branch_bank', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(219, 8, 'dsr', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(220, 8, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(221, 8, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(222, 8, 'user_invitation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(223, 8, 'user_verification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(224, 8, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(225, 8, 'forecast', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(226, 8, 'forecast_budget', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(227, 8, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(228, 8, 'leave_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(229, 8, 'side_letter', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(230, 8, 'setup', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(231, 8, 'rota', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(232, 8, 'recipe', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(233, 9, 'dashboard', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(234, 9, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(235, 9, 'branch', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(236, 9, 'department', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(237, 9, 'notification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(238, 9, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(239, 9, 'staff', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(240, 9, 'leave_center', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(241, 9, 'resignation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(242, 9, 'category', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(243, 9, 'media', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(244, 9, 'playlist', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(245, 9, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(246, 9, 'branch_card', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(247, 9, 'branch_bank', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(248, 9, 'dsr', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(249, 9, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(250, 9, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(251, 9, 'user_invitation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(252, 9, 'user_verification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(253, 9, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(254, 9, 'forecast', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(255, 9, 'forecast_budget', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(256, 9, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(257, 9, 'leave_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(258, 9, 'side_letter', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(259, 9, 'setup', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(260, 9, 'rota', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(261, 9, 'recipe', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(262, 10, 'dashboard', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(263, 10, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(264, 10, 'branch', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(265, 10, 'department', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(266, 10, 'notification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(267, 10, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(268, 10, 'staff', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(269, 10, 'leave_center', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(270, 10, 'resignation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(271, 10, 'category', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(272, 10, 'media', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(273, 10, 'playlist', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(274, 10, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(275, 10, 'branch_card', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(276, 10, 'branch_bank', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(277, 10, 'dsr', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(278, 10, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(279, 10, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(280, 10, 'user_invitation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(281, 10, 'user_verification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(282, 10, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(283, 10, 'forecast', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(284, 10, 'forecast_budget', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(285, 10, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(286, 10, 'leave_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(287, 10, 'side_letter', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(288, 10, 'setup', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(289, 10, 'rota', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(290, 10, 'recipe', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(291, 11, 'dashboard', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(292, 11, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(293, 11, 'branch', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(294, 11, 'department', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(295, 11, 'notification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(296, 11, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(297, 11, 'staff', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(298, 11, 'leave_center', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(299, 11, 'resignation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(300, 11, 'category', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(301, 11, 'media', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(302, 11, 'playlist', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(303, 11, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(304, 11, 'branch_card', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(305, 11, 'branch_bank', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(306, 11, 'dsr', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(307, 11, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(308, 11, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(309, 11, 'user_invitation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(310, 11, 'user_verification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(311, 11, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(312, 11, 'forecast', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(313, 11, 'forecast_budget', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(314, 11, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(315, 11, 'leave_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(316, 11, 'side_letter', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(317, 11, 'setup', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(318, 11, 'rota', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(319, 11, 'recipe', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(320, 12, 'dashboard', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(321, 12, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(322, 12, 'branch', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(323, 12, 'department', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(324, 12, 'notification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(325, 12, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(326, 12, 'staff', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(327, 12, 'leave_center', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(328, 12, 'resignation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(329, 12, 'category', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(330, 12, 'media', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(331, 12, 'playlist', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(332, 12, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(333, 12, 'branch_card', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(334, 12, 'branch_bank', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(335, 12, 'dsr', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(336, 12, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(337, 12, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(338, 12, 'user_invitation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(339, 12, 'user_verification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(340, 12, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(341, 12, 'forecast', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(342, 12, 'forecast_budget', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(343, 12, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(344, 12, 'leave_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(345, 12, 'side_letter', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(346, 12, 'setup', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(347, 12, 'rota', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(348, 12, 'recipe', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(349, 13, 'dashboard', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(350, 13, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(351, 13, 'branch', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(352, 13, 'department', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(353, 13, 'notification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(354, 13, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(355, 13, 'staff', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(356, 13, 'leave_center', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(357, 13, 'resignation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(358, 13, 'category', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(359, 13, 'media', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(360, 13, 'playlist', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(361, 13, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(362, 13, 'branch_card', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(363, 13, 'branch_bank', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(364, 13, 'dsr', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(365, 13, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(366, 13, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(367, 13, 'user_invitation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(368, 13, 'user_verification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(369, 13, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(370, 13, 'forecast', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(371, 13, 'forecast_budget', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(372, 13, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(373, 13, 'leave_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(374, 13, 'side_letter', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(375, 13, 'setup', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(376, 13, 'rota', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(377, 13, 'recipe', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(378, 14, 'dashboard', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(379, 14, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(380, 14, 'branch', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(381, 14, 'department', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(382, 14, 'notification', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(383, 14, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(384, 14, 'staff', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(385, 14, 'leave_center', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(386, 14, 'resignation', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(387, 14, 'category', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(388, 14, 'media', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(389, 14, 'playlist', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(390, 14, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(391, 14, 'branch_card', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(392, 14, 'branch_bank', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(393, 14, 'dsr', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(394, 14, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(395, 14, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(396, 14, 'user_invitation', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(397, 14, 'user_verification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(398, 14, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(399, 14, 'forecast', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(400, 14, 'forecast_budget', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(401, 14, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(402, 14, 'leave_report', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(403, 14, 'side_letter', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(404, 14, 'setup', 3, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(405, 14, 'rota', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(406, 14, 'recipe', 1, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(407, 15, 'dashboard', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(408, 15, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(409, 15, 'branch', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(410, 15, 'department', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(411, 15, 'notification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(412, 15, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(413, 15, 'staff', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(414, 15, 'leave_center', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(415, 15, 'resignation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(416, 15, 'category', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(417, 15, 'media', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(418, 15, 'playlist', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(419, 15, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(420, 15, 'branch_card', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(421, 15, 'branch_bank', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(422, 15, 'dsr', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(423, 15, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(424, 15, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(425, 15, 'user_invitation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(426, 15, 'user_verification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(427, 15, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(428, 15, 'forecast', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(429, 15, 'forecast_budget', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(430, 15, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(431, 15, 'leave_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(432, 15, 'side_letter', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(433, 15, 'setup', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(434, 15, 'rota', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(435, 15, 'recipe', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(436, 16, 'dashboard', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(437, 16, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(438, 16, 'branch', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(439, 16, 'department', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(440, 16, 'notification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(441, 16, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(442, 16, 'staff', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(443, 16, 'leave_center', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(444, 16, 'resignation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(445, 16, 'category', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(446, 16, 'media', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(447, 16, 'playlist', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(448, 16, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(449, 16, 'branch_card', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(450, 16, 'branch_bank', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(451, 16, 'dsr', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(452, 16, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(453, 16, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(454, 16, 'user_invitation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(455, 16, 'user_verification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(456, 16, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(457, 16, 'forecast', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(458, 16, 'forecast_budget', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(459, 16, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(460, 16, 'leave_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(461, 16, 'side_letter', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(462, 16, 'setup', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(463, 16, 'rota', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(464, 16, 'recipe', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(465, 17, 'dashboard', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(466, 17, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(467, 17, 'branch', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(468, 17, 'department', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(469, 17, 'notification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(470, 17, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(471, 17, 'staff', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(472, 17, 'leave_center', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(473, 17, 'resignation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(474, 17, 'category', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(475, 17, 'media', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(476, 17, 'playlist', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(477, 17, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(478, 17, 'branch_card', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(479, 17, 'branch_bank', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(480, 17, 'dsr', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(481, 17, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(482, 17, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(483, 17, 'user_invitation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(484, 17, 'user_verification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(485, 17, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(486, 17, 'forecast', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(487, 17, 'forecast_budget', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(488, 17, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(489, 17, 'leave_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(490, 17, 'side_letter', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(491, 17, 'setup', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(492, 17, 'rota', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(493, 17, 'recipe', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(494, 18, 'dashboard', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(495, 18, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(496, 18, 'branch', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(497, 18, 'department', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(498, 18, 'notification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(499, 18, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(500, 18, 'staff', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(501, 18, 'leave_center', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(502, 18, 'resignation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(503, 18, 'category', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(504, 18, 'media', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(505, 18, 'playlist', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(506, 18, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(507, 18, 'branch_card', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(508, 18, 'branch_bank', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(509, 18, 'dsr', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(510, 18, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(511, 18, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(512, 18, 'user_invitation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(513, 18, 'user_verification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(514, 18, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(515, 18, 'forecast', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(516, 18, 'forecast_budget', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(517, 18, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(518, 18, 'leave_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(519, 18, 'side_letter', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(520, 18, 'setup', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(521, 18, 'rota', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(522, 18, 'recipe', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(523, 19, 'dashboard', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(524, 19, 'user', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(525, 19, 'branch', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(526, 19, 'department', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(527, 19, 'notification', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(528, 19, 'setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(529, 19, 'staff', 1, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(530, 19, 'leave_center', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(531, 19, 'resignation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(532, 19, 'category', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(533, 19, 'media', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(534, 19, 'playlist', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(535, 19, 'activity_log', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(536, 19, 'branch_card', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(537, 19, 'branch_bank', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(538, 19, 'dsr', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(539, 19, 'dsr_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(540, 19, 'change_request', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(541, 19, 'user_invitation', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(542, 19, 'user_verification', 3, 1, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(543, 19, 'employee_contract', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(544, 19, 'forecast', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(545, 19, 'forecast_budget', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(546, 19, 'leave_setting', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(547, 19, 'leave_report', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(548, 19, 'side_letter', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(549, 19, 'setup', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(550, 19, 'rota', 0, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05'),
(551, 19, 'recipe', 1, 0, 1, 1, '2025-06-30 09:44:05', '2025-06-30 09:44:05');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `nv_permissions`
--
ALTER TABLE `nv_permissions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `role_id` (`role_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `nv_permissions`
--
ALTER TABLE `nv_permissions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=552;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `nv_permissions`
--
ALTER TABLE `nv_permissions`
  ADD CONSTRAINT `nv_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `nv_roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
