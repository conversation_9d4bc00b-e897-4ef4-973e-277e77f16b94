-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1deb3
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 04, 2025 at 11:06 AM
-- Server version: 8.0.42-0ubuntu0.24.04.1
-- PHP Version: 8.3.6

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `organization_manager`
--

-- --------------------------------------------------------

--
-- Table structure for table `mo_roles`
--

CREATE TABLE `mo_roles` (
  `id` int NOT NULL,
  `role_name` varchar(255) NOT NULL,
  `role_status` enum('active','inactive') DEFAULT NULL,
  `parent_role_id` int DEFAULT NULL,
  `platform` int DEFAULT NULL,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `organization_id` varchar(255) DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `mo_roles`
--

INSERT INTO `mo_roles` (`id`, `role_name`, `role_status`, `parent_role_id`, `platform`, `created_by`, `updated_by`, `organization_id`, `createdAt`, `updatedAt`) VALUES
(1, 'Super Admin', 'active', NULL, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(2, 'Admin', 'active', 1, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(3, 'Director', 'active', 2, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(4, 'HR', 'active', 2, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(5, 'Area Manager', 'active', 4, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(6, 'Accountant', 'active', 5, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(7, 'Branch Manager', 'active', 6, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(8, 'Assist. Branch Manager', 'active', 7, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(9, 'Head Chef', 'active', 7, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(10, 'Bar Manager', 'active', 7, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(11, 'FOH', 'active', 7, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(12, 'Bar', 'active', 7, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(13, 'Kitchen', 'active', 7, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(14, 'Hotel Manager', 'active', 6, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(15, 'Assist. Hotel Manager', 'active', 14, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(16, 'Receptionist', 'active', 15, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(17, 'Head Housekeeper', 'active', 15, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(18, 'House Keeper', 'active', 17, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51'),
(19, 'Signature', 'active', 2, 1, NULL, NULL, NULL, '2025-04-22 06:47:51', '2025-04-22 06:47:51');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `mo_roles`
--
ALTER TABLE `mo_roles`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `mo_roles`
--
ALTER TABLE `mo_roles`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=799;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
