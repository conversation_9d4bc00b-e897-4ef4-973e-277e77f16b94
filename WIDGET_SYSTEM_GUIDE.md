# Widget Management System

A comprehensive widget management system with role-based permissions, CRUD operations, and seamless integration with the existing module system.

## 📋 Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Database Schema](#database-schema)
- [API Endpoints](#api-endpoints)
- [Usage Examples](#usage-examples)
- [Integration](#integration)
- [Migration](#migration)

## 🎯 Overview

The Widget Management System allows organizations to create, manage, and control access to custom widgets through a role-based permission system. Widgets can be charts, numbers, percentages, tables, lists, cards, or metrics with configurable filters and display options.

## ✨ Features

### Core Features
- **Complete CRUD Operations**: Create, Read, Update, Delete widgets
- **Role-Based Permissions**: Control widget access per role with granular permissions
- **Widget Types**: Support for multiple widget types (chart, number, percentage, table, list, card, metric)
- **Sub-Types**: Detailed sub-categorization (pie, line, bar, donut, etc.)
- **Configurable Filters**: JSON-based filter system for dynamic widget behavior
- **Organization Isolation**: Multi-tenant support with organization-level isolation
- **Order Management**: Custom ordering of widgets per role
- **Validation**: Comprehensive input validation and error handling

### Permission Features
- **Granular Permissions**: 16-level permission system (0-15)
- **Bulk Permission Updates**: Update multiple widget permissions at once
- **Permission Copying**: Copy permissions from one role to multiple roles
- **Default Permissions**: Automatic permission assignment with fallback to view-only
- **Platform-Aware**: Support for web/mobile platform-specific permissions

## 🗄️ Database Schema

### MOWidget Table
```sql
CREATE TABLE mo_widgets (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  widget TEXT NOT NULL,
  type ENUM('chart','number','percentage','table','list','card','metric'),
  subType ENUM('pie','line','bar','donut','area','column','gauge','counter','progress','simple_table','data_table','bullet_list','numbered_list','info_card','stat_card','kpi','summary'),
  filters JSON,
  slug VARCHAR(100) NOT NULL,
  organization_id VARCHAR(255) NOT NULL,
  created_by INT NOT NULL,
  updated_by INT NOT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_widget_slug_per_org (slug, organization_id)
);
```

### MOPermission Table Updates
```sql
ALTER TABLE mo_permissions 
ADD COLUMN widget_id INT DEFAULT NULL,
ADD COLUMN order INT NOT NULL DEFAULT 0,
ADD FOREIGN KEY (widget_id) REFERENCES mo_widgets(id);
```

## 🚀 API Endpoints

### Widget CRUD Operations

#### Create Widget
```http
POST /api/widget
Content-Type: application/json

{
  "name": "User Statistics",
  "widget": "{\"title\": \"User Statistics\", \"data\": \"user_count\"}",
  "type": "number",
  "subType": "counter",
  "filters": [{"label": "Current Month", "value": "month"}],
  "slug": "user_statistics"
}
```

#### Get Widgets
```http
GET /api/widget?page=1&size=10&search=user&type=chart&role_id=1
```

#### Get Widget by ID
```http
GET /api/widget/1
```

#### Update Widget
```http
PUT /api/widget/1
Content-Type: application/json

{
  "name": "Updated User Statistics",
  "filters": [{"label": "Current Year", "value": "year"}]
}
```

#### Delete Widget
```http
DELETE /api/widget/1
```

### Widget Permission Management

#### Update Widget Permissions
```http
PUT /api/role/permissions/widget
Content-Type: application/json

{
  "role_id": 1,
  "widgets": [
    {
      "widget_id": 1,
      "permission": 15,
      "order": 1
    },
    {
      "widget_id": 2,
      "permission": 1,
      "order": 2
    }
  ]
}
```

#### Delete Widget Permissions
```http
DELETE /api/role/permissions/widget/1
```

#### Copy Widget Permissions
```http
POST /api/role/permissions/widget/copy
Content-Type: application/json

{
  "from_role": 1,
  "to_role": [2, 3, 4]
}
```

### Module Integration

#### Get Widgets via Modules API
```http
GET /api/role/modules?type=widget&role_id=1
```

## 💡 Usage Examples

### Creating a Chart Widget
```javascript
const chartWidget = {
  name: "Sales Performance",
  widget: JSON.stringify({
    title: "Monthly Sales",
    chartType: "line",
    dataSource: "sales_data",
    config: {
      showLegend: true,
      showTooltip: true,
      colors: ["#3498db", "#e74c3c"]
    }
  }),
  type: "chart",
  subType: "line",
  filters: [
    { label: "Last 6 Months", value: "6m" },
    { label: "Last 12 Months", value: "12m" },
    { label: "Current Year", value: "year" }
  ],
  slug: "sales_performance"
};
```

### Setting Widget Permissions
```javascript
const permissions = {
  role_id: 2,
  widgets: [
    {
      widget_id: 1,
      permission: 15, // Full access
      order: 1
    },
    {
      widget_id: 2,
      permission: 1,  // View only
      order: 2
    },
    {
      widget_id: 3,
      permission: 7,  // View, create, edit
      order: 3
    }
  ]
};
```

### Permission Levels
- **0**: No access
- **1**: View only
- **3**: View + Create
- **7**: View + Create + Edit
- **15**: Full access (View + Create + Edit + Delete)

## 🔗 Integration

### With Existing Module System
The widget system integrates seamlessly with the existing module system:

1. **getModules API**: When `type=widget` is passed, returns widgets instead of modules
2. **Permission System**: Uses the same MOPermission table with widget_id field
3. **Role Management**: Leverages existing role-based access control
4. **Organization Isolation**: Maintains multi-tenant architecture

### Frontend Integration
```javascript
// Fetch widgets for current user's role
const widgets = await fetch('/api/role/modules?type=widget&role_id=' + userRoleId);

// Fetch specific widget data
const widget = await fetch('/api/widget/' + widgetId);

// Check widget permissions
const hasEditPermission = widget.permission >= 7;
```

## 🔄 Migration

### Running the Migration
```sql
SOURCE migrations/create-widget-system.sql;
```

### Migration Steps
1. Creates `mo_widgets` table with proper indexes
2. Adds `widget_id` and `order` columns to `mo_permissions`
3. Removes deprecated `type` and `order` columns from `mo_modules`
4. Inserts sample widgets and permissions
5. Creates performance indexes

### Post-Migration Verification
```sql
-- Verify widget table
SELECT COUNT(*) FROM mo_widgets;

-- Verify permission table structure
DESCRIBE mo_permissions;

-- Check sample data
SELECT * FROM mo_widgets LIMIT 5;
```

## 🛡️ Security Features

- **Organization Isolation**: All operations are scoped to user's organization
- **Role-Based Access**: Granular permission control per role
- **Input Validation**: Comprehensive validation for all inputs
- **SQL Injection Protection**: Parameterized queries and ORM usage
- **Duplicate Prevention**: Unique constraints and validation checks

## 📊 Performance Considerations

- **Indexed Queries**: Proper indexing on frequently queried columns
- **Pagination Support**: Built-in pagination for large datasets
- **Efficient Joins**: Optimized queries for permission checking
- **Caching Ready**: Structure supports caching implementations

## 🔧 Customization

### Adding New Widget Types
1. Update the `widget_type` enum in `MOWidget.ts`
2. Update the `widget_sub_type` enum for new sub-types
3. Update validators in `widget.validator.ts`
4. Run database migration to update enum values

### Custom Filters
Filters are stored as JSON and can be customized per widget:
```json
[
  {
    "label": "Time Range",
    "type": "select",
    "options": [
      {"label": "Last 7 Days", "value": "7d"},
      {"label": "Last 30 Days", "value": "30d"}
    ]
  },
  {
    "label": "Department",
    "type": "multi-select",
    "source": "departments_api"
  }
]
```

This widget system provides a robust foundation for dashboard and analytics features while maintaining security, performance, and scalability! 🎉
