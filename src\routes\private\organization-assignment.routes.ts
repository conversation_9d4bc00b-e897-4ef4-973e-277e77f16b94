import { Router } from "express";
import {
  assignModulesToOrganizations,
  assignWidgetsToOrganizations,
  getOrganizationModules,
  getOrganizationWidgets,
  removeModuleAssignments,
  removeWidgetAssignments
} from "../../controller/organization-assignment.controller";

const router = Router();

/**
 * @route POST /api/organization-assignment/assign-modules
 * @desc Assign modules to organizations
 * @access Private (Admin only)
 * @body { module_ids: number[], organization_ids: number[] }
 */
router.post("/assign-modules", assignModulesToOrganizations);

/**
 * @route POST /api/organization-assignment/assign-widgets
 * @desc Assign widgets to organizations
 * @access Private (Admin only)
 * @body { widget_ids: number[], organization_ids: number[] }
 */
router.post("/assign-widgets", assignWidgetsToOrganizations);

/**
 * @route GET /api/organization-assignment/organization/:organization_id/modules
 * @desc Get modules assigned to an organization
 * @access Private
 */
router.get("/organization/:organization_id/modules", getOrganizationModules);

/**
 * @route GET /api/organization-assignment/organization/:organization_id/widgets
 * @desc Get widgets assigned to an organization
 * @access Private
 */
router.get("/organization/:organization_id/widgets", getOrganizationWidgets);

/**
 * @route DELETE /api/organization-assignment/remove-module-assignments
 * @desc Remove module assignments from organizations
 * @access Private (Admin only)
 * @body { module_ids: number[], organization_ids: number[] }
 */
router.delete("/remove-module-assignments", removeModuleAssignments);

/**
 * @route DELETE /api/organization-assignment/remove-widget-assignments
 * @desc Remove widget assignments from organizations
 * @access Private (Admin only)
 * @body { widget_ids: number[], organization_ids: number[] }
 */
router.delete("/remove-widget-assignments", removeWidgetAssignments);

export default router;
