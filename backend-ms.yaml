apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-ms
spec:
  selector:
    matchLabels:
      app: backend-ms
  template:
    metadata:
      labels:
        app: backend-ms
    spec:
      containers:
      - name: backend-ms
        image: backend-ms:2.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 9023
        command: ["node", "./build/index.js"]
        envFrom:
        - configMapRef:
            name: micro-office-config
        volumeMounts:
        - name: uploads
          mountPath: /usr/src/backend-ms/build/uploads
        - name: sequelize-config # This is the name of the volume defined below
          mountPath: /usr/src/backend-ms/.sequelizerc # Path where the .sequelizerc file will be mounted in the container
          subPath: .sequelizerc # The key from the ConfigMap 'sequelize-config' to mount as a file
      volumes:
      - name: uploads
        persistentVolumeClaim:
          claimName: uploads-pvc
      - name: sequelize-config
        configMap:
          name: sequelize-config
---
apiVersion: v1
kind: Service
metadata:
  name: backend-ms-service      
spec:
  selector:
    app: backend-ms
  ports:
  - protocol: TCP
    port: 9023
    targetPort: 9023
    nodePort: 30029
  type: NodePort