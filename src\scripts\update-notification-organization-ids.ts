import fs from 'fs';
import path from 'path';

// <PERSON>ript to update all sendEmailNotification calls to include organization_id
// This script analyzes the codebase and suggests updates for templateData objects

interface NotificationCall {
  file: string;
  line: number;
  context: string;
  templateDataName: string;
  hasOrganizationId: boolean;
  suggestedFix: string;
}

const analyzeNotificationCalls = () => {
  const results: NotificationCall[] = [];
  
  // Common patterns found in the codebase analysis
  const notificationPatterns = [
    {
      file: 'src/controller/user.controller.ts',
      line: 3850,
      context: 'reset_user_pin',
      templateDataName: 'templateData',
      hasOrganizationId: false,
      suggestedFix: 'templateData.organization_id = req.user.organization_id;'
    },
    {
      file: 'src/controller/user.controller.ts', 
      line: 3855,
      context: 'reset_password_mail',
      templateDataName: 'templateData',
      hasOrganizationId: false,
      suggestedFix: 'templateData.organization_id = req.user.organization_id;'
    },
    {
      file: 'src/helper/common.ts',
      line: 2645,
      context: 'send_invitation',
      templateDataName: 'templateData',
      hasOrganizationId: false,
      suggestedFix: 'templateData.organization_id = pendingUser.organization_id; // Already available'
    },
    {
      file: 'src/helper/common.ts',
      line: 3462,
      context: 'onboarding_expiring',
      templateDataName: 'templateData',
      hasOrganizationId: true,
      suggestedFix: 'Already has organization_id'
    },
    {
      file: 'src/helper/common.ts',
      line: 3537,
      context: 'onboarding_expiring_staff',
      templateDataName: 'templateData',
      hasOrganizationId: false,
      suggestedFix: 'templateData.organization_id = findBranch?.organization_id;'
    },
    {
      file: 'src/helper/common.ts',
      line: 4269,
      context: 'probation_expiring_staff',
      templateDataName: 'templateData',
      hasOrganizationId: false,
      suggestedFix: 'templateData.organization_id = findBranch?.organization_id;'
    },
    {
      file: 'src/controller/request.controller.ts',
      line: 301,
      context: 'leave_request',
      templateDataName: 'templateData',
      hasOrganizationId: false,
      suggestedFix: 'templateData.organization_id = req.user.organization_id;'
    },
    {
      file: 'src/controller/request.controller.ts',
      line: 1134,
      context: 'leave_action',
      templateDataName: 'templateData',
      hasOrganizationId: false,
      suggestedFix: 'templateData.organization_id = req.user.organization_id;'
    },
    {
      file: 'src/controller/onbording.controller.ts',
      line: 1445,
      context: 'form_request',
      templateDataName: 'templateData',
      hasOrganizationId: false,
      suggestedFix: 'templateData.organization_id = req.user.organization_id;'
    },
    {
      file: 'src/controller/auth.controller.ts',
      line: 606,
      context: 'reset_password_success',
      templateDataName: 'templateData',
      hasOrganizationId: false,
      suggestedFix: 'templateData.organization_id = req.user.organization_id;'
    },
    {
      file: 'src/helper/cron.service.ts',
      line: 71,
      context: 'onboarding_pending',
      templateDataName: 'templateData',
      hasOrganizationId: false,
      suggestedFix: 'templateData.organization_id = user.organization_id;'
    },
    {
      file: 'src/services/auth.service.ts',
      line: 196,
      context: 'staff_invitation_resend',
      templateDataName: 'templateData',
      hasOrganizationId: false,
      suggestedFix: 'templateData.organization_id = mailData.organization_id; // Already available'
    }
  ];

  return notificationPatterns;
};

const generateUpdateScript = () => {
  const calls = analyzeNotificationCalls();
  
  console.log('🔍 Analysis of sendEmailNotification calls in codebase:\n');
  
  let needsUpdate = 0;
  let alreadyCorrect = 0;
  
  calls.forEach((call, index) => {
    console.log(`${index + 1}. File: ${call.file}`);
    console.log(`   Line: ${call.line}`);
    console.log(`   Context: ${call.context}`);
    console.log(`   Template Data: ${call.templateDataName}`);
    console.log(`   Has organization_id: ${call.hasOrganizationId ? '✅' : '❌'}`);
    console.log(`   Suggested Fix: ${call.suggestedFix}`);
    console.log('');
    
    if (call.hasOrganizationId) {
      alreadyCorrect++;
    } else {
      needsUpdate++;
    }
  });
  
  console.log(`📊 Summary:`);
  console.log(`   • Total calls analyzed: ${calls.length}`);
  console.log(`   • Already have organization_id: ${alreadyCorrect}`);
  console.log(`   • Need organization_id added: ${needsUpdate}`);
  console.log('');
  
  // Generate specific update instructions
  console.log('🛠️  Specific Update Instructions:\n');
  
  const updateInstructions = [
    {
      file: 'src/controller/user.controller.ts',
      description: 'Add organization_id to reset pin/password notifications',
      code: `
// Around line 3845, add organization_id to templateData:
templateData.organization_id = req.user.organization_id;

// This should be added before the sendEmailNotification calls
if (reset_type == "pin") {
  templateData.pin = user_login_pin;
  templateData.mail_type = "reset_user_pin";
  templateData.organization_id = req.user.organization_id; // Add this line
  await sendEmailNotification(templateData);
}
if (reset_type == "password") {
  templateData.password = user_password;
  templateData.mail_type = "reset_password_mail";
  templateData.organization_id = req.user.organization_id; // Add this line
  await sendEmailNotification(templateData);
}`
    },
    {
      file: 'src/controller/request.controller.ts',
      description: 'Add organization_id to leave request notifications',
      code: `
// Around line 291, add organization_id to templateData:
const templateData: any = {
  name: \`\${getUserDetail.user_first_name} \${getUserDetail.user_last_name}\`,
  date: \`\${moment(start_date).format("DD/MM/YYYY")} to \${moment(end_date).format("DD/MM/YYYY")}\`,
  type: getLeaveTypeName?.name,
  reason: request_reason,
  ORGANIZATION_LOGO: await getOrganizationLogo(req.user.organization_id),
  LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
  ADDRESS: EMAIL_ADDRESS.ADDRESS,
  PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
  EMAIL: EMAIL_ADDRESS.EMAIL,
  ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
  smtpConfig: 'INFO',
  mail_type: 'leave_request',
  organization_id: req.user.organization_id // Add this line
};

// Around line 1084, add organization_id to leave action templateData:
const templateData: any = {
  name: \`\${getRequestUserDetail.user_first_name} \${getRequestUserDetail.user_last_name}\`,
  date: \`\${moment(findLeaveRequest.start_date).format("DD/MM/YYYY")} to \${moment(findLeaveRequest.end_date).format("DD/MM/YYYY")}\`,
  admin: req.user.user_first_name,
  remark: remark,
  ORGANIZATION_LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
  LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
  ADDRESS: EMAIL_ADDRESS.ADDRESS,
  PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
  EMAIL: EMAIL_ADDRESS.EMAIL,
  ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
  smtpConfig: 'INFO',
  mail_type: 'leave_action',
  organization_id: req.user.organization_id // Add this line
};`
    },
    {
      file: 'src/helper/common.ts',
      description: 'Add organization_id to staff expiring notifications',
      code: `
// Around line 3536, add organization_id to onboarding_expiring_staff templateData:
const templateData = {
  user_list,
  name: bm_user.user_first_name,
  branch_name: findBranch?.branch_name,
  email: process.env.NEXT_NODE_ENV !== "staging" ? "<EMAIL>" : bm_user.user_email,
  mail_type: "onboarding_expiring_staff",
  ORGANIZATION_LOGO: await getOrganizationLogo(findBranch?.organization_id),
  LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
  ADDRESS: EMAIL_ADDRESS.ADDRESS,
  PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
  EMAIL: EMAIL_ADDRESS.EMAIL,
  ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
  smtpConfig: "INFO",
  organization_id: findBranch?.organization_id // Add this line
};

// Around line 4268, add organization_id to probation_expiring_staff templateData:
const templateData = {
  user_list,
  name: bm_user.user_first_name,
  branch_name: findBranch?.branch_name,
  email: bm_user.user_email,
  mail_type: "probation_expiring_staff",
  ORGANIZATION_LOGO: await getOrganizationLogo(findBranch?.organization_id),
  LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
  ADDRESS: EMAIL_ADDRESS.ADDRESS,
  PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
  EMAIL: EMAIL_ADDRESS.EMAIL,
  ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
  smtpConfig: "INFO",
  organization_id: findBranch?.organization_id // Add this line
};`
    },
    {
      file: 'src/controller/auth.controller.ts',
      description: 'Add organization_id to password reset success notification',
      code: `
// Around line 594, add organization_id to templateData:
const templateData: any = {
  name: CheckEmailExist.user_first_name,
  email: CheckEmailExist.user_email,
  mail_type: 'reset_password_success',
  ORGANIZATION_LOGO: await getOrganizationLogo(req.user.organization_id),
  LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
  ADDRESS: EMAIL_ADDRESS.ADDRESS,
  PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
  EMAIL: EMAIL_ADDRESS.EMAIL,
  ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
  smtpConfig: 'INFO',
  organization_id: req.user.organization_id // Add this line
};`
    }
  ];
  
  updateInstructions.forEach((instruction, index) => {
    console.log(`${index + 1}. ${instruction.file}`);
    console.log(`   ${instruction.description}`);
    console.log(`   Code changes:${instruction.code}`);
    console.log('');
  });
  
  return {
    totalCalls: calls.length,
    needsUpdate: needsUpdate,
    alreadyCorrect: alreadyCorrect,
    updateInstructions: updateInstructions
  };
};

// Auto-run the script if executed directly
if (require.main === module) {
  console.log('🚀 Starting notification organization_id update analysis...\n');
  
  const results = generateUpdateScript();
  
  console.log('✅ Analysis completed!');
  console.log(`📈 Results: ${results.needsUpdate} files need updates, ${results.alreadyCorrect} already correct`);
  console.log('\n💡 Next steps:');
  console.log('1. Apply the suggested code changes above');
  console.log('2. Test the notification preference system');
  console.log('3. Verify organization_id is properly passed to all notifications');
}

export { analyzeNotificationCalls, generateUpdateScript };
