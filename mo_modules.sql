-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1deb5ubuntu1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Jun 12, 2025 at 01:24 PM
-- Server version: 8.0.42-0ubuntu0.22.04.1
-- PHP Version: 8.3.20

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `organization_manager`
--

-- --------------------------------------------------------

--
-- Table structure for table `mo_modules`
--

CREATE TABLE `mo_modules` (
  `id` int NOT NULL,
  `module` varchar(255) NOT NULL,
  `module_name` varchar(255) NOT NULL,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `mo_modules`
--

INSERT INTO `mo_modules` (`id`, `module`, `module_name`, `created_by`, `updated_by`, `createdAt`, `updatedAt`) VALUES
(1, 'dashboard', 'Dashboard', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(2, 'budget_forecast', 'Budget & Forecast', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(3, 'company_setting', 'Comapny Settings', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(4, 'branch', 'Branches', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(5, 'training', 'Training Assignments', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(6, 'dsr_setting', 'DSR settings', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(7, 'department', 'Departments', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(8, 'holiday_management', 'Holiday Management', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(9, 'leave_setting', 'Leave Configuration', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(10, 'administrator_account', 'Administrator Accounts', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(11, 'export_setting', 'Export Settings', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(12, 'change_request_setting', 'Change Request Setting', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(13, 'staff', 'All staff', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(14, 'staff_invitation', 'Staff Invitation', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(15, 'employee_renewal', 'Contract Renewal', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(16, 'change_request', 'Change Request', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(17, 'resignation', 'Resignation', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(18, 'dsr', 'DSR', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(19, 'dsr_request', 'DSR Request', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(20, 'wsr', 'WSR', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(21, 'wsr_request', 'WSR Request', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(22, 'expense', 'Expenses', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(23, 'expense_request', 'Expenses Request', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(24, 'dsr_report', 'Logbook Reports', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(25, 'team_leave', 'Team Leave', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(26, 'my_leave', 'My Leave', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(27, 'leave_report', 'Leave Reports', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(28, 'rota_dashboard', 'Rota Dashboard', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(29, 'rotas', 'Rotas', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(30, 'availability', 'Availability', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(31, 'staff_notification', 'Notification Staff', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(32, 'own_notification', 'Notification Personal', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(33, 'staff_document', 'Document Staff', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(34, 'own_document', 'Document Personal', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(35, 'activity_log', 'Activity Logs', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(36, 'permission', 'Roles Permissions', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(37, 'role', 'Roles', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(38, 'module', 'Modules', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54');

INSERT INTO `mo_modules` (`id`, `module`, `module_name`, `created_by`, `updated_by`, `createdAt`, `updatedAt`) VALUES
(39, 'recipe_dashboard', 'Recipe Dashboard', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(40, 'recipe_ingredients_category', 'Ingredients Category', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(41, 'recipe_category', 'Recipe Category', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(42, 'recipe_allergens', 'Allergens', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(43, 'recipe_ingredients', 'Ingredient', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(44, 'recipe', 'Recipes', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54'),
(45, 'recipe_setting', 'Recipe Setting', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54');

INSERT INTO `mo_modules` (`id`, `module`, `module_name`, `created_by`, `updated_by`, `createdAt`, `updatedAt`) VALUES
(46, 'user_verification', 'User Verification', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54');

INSERT INTO `mo_modules` (`id`, `module`, `module_name`, `created_by`, `updated_by`, `createdAt`, `updatedAt`) VALUES
(47, 'support_ticket', 'Support Ticket', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54');

INSERT INTO `mo_modules` (`id`, `module`, `module_name`, `created_by`, `updated_by`, `createdAt`, `updatedAt`) VALUES
(48, 'support_ticket_dashboard', 'Support Dashboard', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54');


INSERT INTO `mo_modules` (`id`, `module`, `module_name`, `created_by`, `updated_by`, `createdAt`, `updatedAt`) VALUES
(49, 'employee_contract', 'Contracts', NULL, NULL, '2025-06-12 09:23:54', '2025-06-12 09:23:54')
--
-- Indexes for dumped tables
--

--
-- Indexes for table `mo_modules`
--
ALTER TABLE `mo_modules`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `module` (`module`),
  ADD UNIQUE KEY `module_2` (`module`),
  ADD UNIQUE KEY `module_3` (`module`),
  ADD UNIQUE KEY `module_4` (`module`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `mo_modules`
--
ALTER TABLE `mo_modules`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
