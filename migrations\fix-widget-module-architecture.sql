-- Fix Widget Module Architecture
-- This script fixes the widget system to follow proper junction table architecture
-- Run this after the create-widget-system.sql migration to clean up any organization-specific modules

-- Step 1: Find any organization-specific widget modules that should be global
SELECT 'Checking for organization-specific widget modules...' as message;

-- Step 2: Migrate organization-specific widget modules to junction table
INSERT IGNORE INTO `mo_org_modules` (`module_id`, `organization_id`, `status`, `created_by`, `updated_by`, `createdAt`, `updatedAt`)
SELECT 
    m.id as module_id,
    m.organization_id,
    'active' as status,
    COALESCE(m.created_by, 1) as created_by,
    COALESCE(m.updated_by, 1) as updated_by,
    m.createdAt,
    NOW() as updatedAt
FROM mo_modules m
WHERE m.module = 'widget'
AND m.organization_id IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM mo_org_modules om 
    WHERE om.module_id = m.id 
    AND om.organization_id = m.organization_id
);

-- Step 3: Migrate organization-specific widgets to junction table
INSERT IGNORE INTO `mo_org_widgets` (`widget_id`, `organization_id`, `status`, `created_by`, `updated_by`, `createdAt`, `updatedAt`)
SELECT 
    w.id as widget_id,
    w.organization_id,
    'active' as status,
    COALESCE(w.created_by, 1) as created_by,
    COALESCE(w.updated_by, 1) as updated_by,
    w.createdAt,
    NOW() as updatedAt
FROM mo_widgets w
WHERE w.organization_id IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM mo_org_widgets ow 
    WHERE ow.widget_id = w.id 
    AND ow.organization_id = w.organization_id
);

-- Step 4: Create global widget module if it doesn't exist
INSERT IGNORE INTO `mo_modules` (`module`, `module_name`, `organization_id`, `created_by`, `updated_by`, `createdAt`, `updatedAt`)
SELECT 
    'widget' as module,
    'Widget Management' as module_name,
    NULL as organization_id,
    1 as created_by,
    1 as updated_by,
    NOW() as createdAt,
    NOW() as updatedAt
WHERE NOT EXISTS (
    SELECT 1 FROM mo_modules 
    WHERE module = 'widget' 
    AND organization_id IS NULL
);

-- Step 5: Update organization-specific widget modules to be global
-- First, get the global widget module ID
SET @global_widget_module_id = (
    SELECT id FROM mo_modules 
    WHERE module = 'widget' 
    AND organization_id IS NULL 
    LIMIT 1
);

-- Update junction table references to point to global module
UPDATE mo_org_modules 
SET module_id = @global_widget_module_id
WHERE module_id IN (
    SELECT id FROM mo_modules 
    WHERE module = 'widget' 
    AND organization_id IS NOT NULL
);

-- Update permission references to point to global module
UPDATE mo_permissions 
SET module_id = @global_widget_module_id
WHERE module_id IN (
    SELECT id FROM mo_modules 
    WHERE module = 'widget' 
    AND organization_id IS NOT NULL
);

-- Step 6: Delete organization-specific widget modules (keep only global)
DELETE FROM mo_modules 
WHERE module = 'widget' 
AND organization_id IS NOT NULL;

-- Step 7: Update organization-specific widgets to be global
-- Create global copies of widgets if they don't exist
INSERT IGNORE INTO `mo_widgets` (`name`, `widget`, `type`, `subType`, `filters`, `slug`, `organization_id`, `created_by`, `updated_by`, `createdAt`, `updatedAt`)
SELECT DISTINCT
    w.name,
    w.widget,
    w.type,
    w.subType,
    w.filters,
    w.slug,
    NULL as organization_id,
    COALESCE(MIN(w.created_by), 1) as created_by,
    COALESCE(MAX(w.updated_by), 1) as updated_by,
    MIN(w.createdAt) as createdAt,
    NOW() as updatedAt
FROM mo_widgets w
WHERE w.organization_id IS NOT NULL
GROUP BY w.name, w.widget, w.type, w.subType, w.filters, w.slug
HAVING NOT EXISTS (
    SELECT 1 FROM mo_widgets global_w 
    WHERE global_w.slug = w.slug 
    AND global_w.organization_id IS NULL
);

-- Step 8: Update junction table references to point to global widgets
UPDATE mo_org_widgets ow
INNER JOIN mo_widgets org_widget ON ow.widget_id = org_widget.id
INNER JOIN mo_widgets global_widget ON global_widget.slug = org_widget.slug AND global_widget.organization_id IS NULL
SET ow.widget_id = global_widget.id
WHERE org_widget.organization_id IS NOT NULL;

-- Update permission references to point to global widgets
UPDATE mo_permissions p
INNER JOIN mo_widgets org_widget ON p.widget_id = org_widget.id
INNER JOIN mo_widgets global_widget ON global_widget.slug = org_widget.slug AND global_widget.organization_id IS NULL
SET p.widget_id = global_widget.id
WHERE org_widget.organization_id IS NOT NULL;

-- Step 9: Delete organization-specific widgets (keep only global)
DELETE FROM mo_widgets 
WHERE organization_id IS NOT NULL;

-- Step 10: Ensure all organizations have access to widget module
INSERT IGNORE INTO `mo_org_modules` (`module_id`, `organization_id`, `status`, `created_by`, `updated_by`, `createdAt`, `updatedAt`)
SELECT 
    @global_widget_module_id as module_id,
    o.id as organization_id,
    'active' as status,
    1 as created_by,
    1 as updated_by,
    NOW() as createdAt,
    NOW() as updatedAt
FROM nv_organizations o
WHERE NOT EXISTS (
    SELECT 1 FROM mo_org_modules om 
    WHERE om.module_id = @global_widget_module_id 
    AND om.organization_id = o.id
);

-- Step 11: Ensure all organizations have access to global widgets
INSERT IGNORE INTO `mo_org_widgets` (`widget_id`, `organization_id`, `status`, `created_by`, `updated_by`, `createdAt`, `updatedAt`)
SELECT 
    w.id as widget_id,
    o.id as organization_id,
    'active' as status,
    1 as created_by,
    1 as updated_by,
    NOW() as createdAt,
    NOW() as updatedAt
FROM mo_widgets w
CROSS JOIN nv_organizations o
WHERE w.organization_id IS NULL
AND NOT EXISTS (
    SELECT 1 FROM mo_org_widgets ow 
    WHERE ow.widget_id = w.id 
    AND ow.organization_id = o.id
);

-- Verification queries
SELECT 'Widget Module Architecture Fix Completed!' as message;
SELECT 'Global widget modules:' as info, COUNT(*) as count FROM mo_modules WHERE module = 'widget' AND organization_id IS NULL;
SELECT 'Organization-specific widget modules (should be 0):' as info, COUNT(*) as count FROM mo_modules WHERE module = 'widget' AND organization_id IS NOT NULL;
SELECT 'Global widgets:' as info, COUNT(*) as count FROM mo_widgets WHERE organization_id IS NULL;
SELECT 'Organization-specific widgets (should be 0):' as info, COUNT(*) as count FROM mo_widgets WHERE organization_id IS NOT NULL;
SELECT 'Widget module assignments:' as info, COUNT(*) as count FROM mo_org_modules WHERE module_id = @global_widget_module_id;
SELECT 'Widget assignments:' as info, COUNT(*) as count FROM mo_org_widgets;
