import { Router } from "express";
import {
    createPreference,
    getPreferences,
    getPreferenceById,
    updatePreference,
    deletePreference,
    bulkUpdatePreferences,
    getPreferenceOptions
} from "../../controller/preferences.controller";
import preferencesValidator from "../../validators/preferences.validator";

const router = Router();

// Preference CRUD routes
router.post('/', preferencesValidator.createPreference(), createPreference);
router.get('/', preferencesValidator.getPreferences(), getPreferences);
router.get('/options', getPreferenceOptions);
router.get('/:id', preferencesValidator.getPreferenceById(), getPreferenceById);
router.put('/:id', preferencesValidator.updatePreference(), updatePreference);
router.delete('/:id', preferencesValidator.deletePreference(), deletePreference);

// Bulk operations
router.put('/bulk/update', preferencesValidator.bulkUpdatePreferences(), bulkUpdatePreferences);

export default router;
