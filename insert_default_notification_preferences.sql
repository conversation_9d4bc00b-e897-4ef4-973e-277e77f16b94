-- Insert default notification preferences for all organizations
-- Based on the image provided, these are the 3 main preference types per organization

-- First, let's see what organizations exist
SELECT DISTINCT organization_id FROM nv_users WHERE organization_id IS NOT NULL;

-- Insert default preferences for each organization
-- You'll need to run this for each organization_id

-- Template for inserting preferences (replace 'your_org_id' with actual organization_id)
-- INSERT INTO mo_notification_preferences (key, value, type, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES

-- For organization 'your_org_id', insert the 3 main preference types:

-- 1. Purchased Plan (Email: true, Push: true, Banner: false)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('purchased_plan', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('purchased_plan', true, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('purchased_plan', false, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 2. Upgrade Plan (Email: true, Push: true, Banner: false)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('upgrade_plan', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('upgrade_plan', true, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('upgrade_plan', false, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 3. Downgrade Plan (Email: true, Push: true, Banner: false)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('downgrade_plan', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('downgrade_plan', true, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('downgrade_plan', false, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 4. Cancelled Plan (Email: true, Push: false, Banner: false)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('cancelled_plan', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('cancelled_plan', false, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('cancelled_plan', false, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 5. Renewal Plan from CRON (Email: true, Push: false, Banner: false)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('renewal_plan_cron', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('renewal_plan_cron', false, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('renewal_plan_cron', false, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 6. Trial Plan Assigned (Email: true, Push: false, Banner: false)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('trial_plan_assigned', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('trial_plan_assigned', false, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('trial_plan_assigned', false, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 7. Trial Completion Reminder (Email: true, Push: true, Banner: true)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('trial_completion_reminder', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('trial_completion_reminder', true, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('trial_completion_reminder', true, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 8. Renewal Completion Reminder (Email: true, Push: true, Banner: true)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('renewal_completion_reminder', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('renewal_completion_reminder', true, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('renewal_completion_reminder', true, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 9. Cancellation Reminder (Email: true, Push: true, Banner: true)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('cancellation_reminder', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('cancellation_reminder', true, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('cancellation_reminder', true, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 10. Payment Failed 0 to 4 days (Email: true, Push: false, Banner: false)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('payment_failed_0_4_days', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('payment_failed_0_4_days', false, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('payment_failed_0_4_days', false, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 11. Payment Failed 5 day (Email: true, Push: false, Banner: false)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('payment_failed_5_day', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('payment_failed_5_day', false, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('payment_failed_5_day', false, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 12. Payment Failed 6 day with expiration (Email: true, Push: false, Banner: false)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('payment_failed_6_day_expiration', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('payment_failed_6_day_expiration', false, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('payment_failed_6_day_expiration', false, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 13. User Verification (Email: true, Push: false, Banner: false)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('user_verification', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('user_verification', false, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('user_verification', false, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 14. Forgot Password (Email: true, Push: false, Banner: false)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('forgot_password', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('forgot_password', false, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('forgot_password', false, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 15. Resend OTP (Email: true, Push: false, Banner: false)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('resend_otp', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('resend_otp', false, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('resend_otp', false, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- 16. Super Admin Reactivation (Email: true, Push: false, Banner: false)
INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES
('super_admin_reactivation', true, 'mail', 'your_org_id', 1, 1, NOW(), NOW()),
('super_admin_reactivation', false, 'push', 'your_org_id', 1, 1, NOW(), NOW()),
('super_admin_reactivation', false, 'banner', 'your_org_id', 1, 1, NOW(), NOW());

-- To run this for all organizations, you can use this dynamic query:
-- SELECT CONCAT(
--   'INSERT INTO mo_notification_preferences (`key`, `value`, `type`, organization_id, created_by, updated_by, createdAt, updatedAt) VALUES ',
--   '(''purchased_plan'', true, ''mail'', ''', organization_id, ''', 1, 1, NOW(), NOW());'
-- ) as sql_statement
-- FROM (SELECT DISTINCT organization_id FROM nv_users WHERE organization_id IS NOT NULL) as orgs;
