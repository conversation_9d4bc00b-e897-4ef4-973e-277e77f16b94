import { permittedForAdminEnhanced, permittedForAdminMO } from '../helper/common';
import { User } from '../models/User';
import { Role as MORole } from '../models/MORole';
import { UserRole } from '../models/UserRole';
import { Role } from '../models/Role';
import { ROLE_CONSTANT } from '../helper/constant';

// Mock the models
jest.mock('../models/User');
jest.mock('../models/MORole');
jest.mock('../models/UserRole');
jest.mock('../models/Role');

const mockUser = User as jest.Mocked<typeof User>;
const mockMORole = MORole as jest.Mocked<typeof MORole>;
const mockUserRole = UserRole as jest.Mocked<typeof UserRole>;
const mockRole = Role as jest.Mocked<typeof Role>;

describe('Enhanced Permission System Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('permittedForAdminMO', () => {
    test('should return true when user has MORole with correct permissions', async () => {
      // Mock user with user_role_id set
      mockUser.findOne.mockResolvedValue({
        id: 1,
        user_role_id: 5,
        organization_id: 'org-123',
        user_status: 'active'
      } as any);

      // Mock MORole
      mockMORole.findOne.mockResolvedValue({
        id: 5,
        role_name: ROLE_CONSTANT.ADMIN,
        role_status: 'active',
        organization_id: 'org-123'
      } as any);

      const result = await permittedForAdminMO(
        1,
        'org-123',
        [ROLE_CONSTANT.ADMIN, ROLE_CONSTANT.SUPER_ADMIN]
      );

      expect(result).toBe(true);
    });

    test('should return false when user role not in allowed array', async () => {
      // Mock user with user_role_id set
      mockUser.findOne.mockResolvedValue({
        id: 1,
        user_role_id: 5,
        organization_id: 'org-123',
        user_status: 'active'
      } as any);

      // Mock MORole with different role
      mockMORole.findOne.mockResolvedValue({
        id: 5,
        role_name: 'EMPLOYEE',
        role_status: 'active',
        organization_id: 'org-123'
      } as any);

      const result = await permittedForAdminMO(
        1,
        'org-123',
        [ROLE_CONSTANT.ADMIN, ROLE_CONSTANT.SUPER_ADMIN]
      );

      expect(result).toBe(false);
    });

    test('should fallback to old system when user_role_id is null', async () => {
      // Mock user without user_role_id
      mockUser.findOne.mockResolvedValue({
        id: 1,
        user_role_id: null,
        web_user_active_role_id: 3,
        organization_id: 'org-123',
        user_status: 'active'
      } as any);

      // Mock old role system
      mockUserRole.findAll.mockResolvedValue([
        {
          role: { role_name: ROLE_CONSTANT.ADMIN }
        }
      ] as any);

      const result = await permittedForAdminMO(
        1,
        'org-123',
        [ROLE_CONSTANT.ADMIN]
      );

      expect(result).toBe(true);
    });
  });

  describe('permittedForAdminEnhanced', () => {
    test('should return true when new system grants permission', async () => {
      // Mock successful new system check
      mockUser.findOne.mockResolvedValue({
        id: 1,
        user_role_id: 5,
        organization_id: 'org-123',
        user_status: 'active'
      } as any);

      mockMORole.findOne.mockResolvedValue({
        id: 5,
        role_name: ROLE_CONSTANT.ADMIN,
        role_status: 'active',
        organization_id: 'org-123'
      } as any);

      const result = await permittedForAdminEnhanced(
        1,
        'org-123',
        [ROLE_CONSTANT.ADMIN]
      );

      expect(result).toBe(true);
    });

    test('should fallback to old system when new system fails', async () => {
      // Mock new system failure
      mockUser.findOne.mockResolvedValueOnce({
        id: 1,
        user_role_id: null,
        web_user_active_role_id: 3,
        organization_id: 'org-123',
        user_status: 'active'
      } as any);

      // Mock old system success
      mockUserRole.findAll.mockResolvedValue([
        {
          role: { role_name: ROLE_CONSTANT.ADMIN }
        }
      ] as any);

      const result = await permittedForAdminEnhanced(
        1,
        'org-123',
        [ROLE_CONSTANT.ADMIN]
      );

      expect(result).toBe(true);
    });

    test('should return false when both systems fail', async () => {
      // Mock both systems failing
      mockUser.findOne.mockResolvedValue(null);
      mockUserRole.findAll.mockResolvedValue([]);

      const result = await permittedForAdminEnhanced(
        1,
        'org-123',
        [ROLE_CONSTANT.ADMIN]
      );

      expect(result).toBe(false);
    });
  });
});
