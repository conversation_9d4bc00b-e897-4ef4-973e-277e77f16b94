import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { MOModule } from "../models/MOModule";
import { MOWidget } from "../models/MOWidget";
import { MOOrgModule } from "../models/MOOrgModule";
import { MOOrgWidget } from "../models/MOOrgWidget";
import { MOPermission, status as permission_status } from "../models/MOPermission";
import { Role } from "../models/Role";
import { User } from "../models/User";

/**
 * Assign modules to organizations
 * @param req - Request object containing module_ids and organization_ids
 * @param res - Response object
 */
export const assignModulesToOrganizations = async (req: Request, res: Response) => {
  try {
    const { module_ids, organization_ids } = req.body;

    // Validate required fields
    if (!module_ids || !Array.isArray(module_ids) || module_ids.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "module_ids array is required and cannot be empty"
      });
    }

    if (!organization_ids || !Array.isArray(organization_ids) || organization_ids.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "organization_ids array is required and cannot be empty"
      });
    }

    // Only super admins (organization_id = null) can assign modules to organizations
    if (req.user.organization_id !== null) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Only super admins can assign modules to organizations"
      });
    }

    // Validate that all modules exist
    const modules = await MOModule.findAll({
      where: { id: { [Op.in]: module_ids } },
      attributes: ['id', 'module', 'module_name']
    });

    if (modules.length !== module_ids.length) {
      const foundModuleIds = modules.map(m => m.id);
      const missingModuleIds = module_ids.filter(id => !foundModuleIds.includes(id));
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: `Modules not found: ${missingModuleIds.join(', ')}`
      });
    }

    // Validate that all organizations exist by checking users with those organization_ids
    const organizationUsers = await User.findAll({
      where: {
        organization_id: { [Op.in]: organization_ids.map(id => id.toString()) }
      },
      attributes: ['organization_id'],
      group: ['organization_id'],
      raw: true
    });

    const foundOrgIds = organizationUsers.map(u => u.organization_id);
    const missingOrgIds = organization_ids.filter(id => !foundOrgIds.includes(id.toString()));

    if (missingOrgIds.length > 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: `Organizations not found: ${missingOrgIds.join(', ')}`
      });
    }

    // Create assignments
    const assignments: any[] = [];
    for (const module_id of module_ids) {
      for (const organization_id of organization_ids) {
        assignments.push({
          module_id,
          organization_id: organization_id.toString(),
          status: 'active',
          created_by: req.user.id,
          updated_by: req.user.id
        });
      }
    }

    // Bulk create assignments (will ignore duplicates due to unique constraint)
    const createdAssignments = await MOOrgModule.bulkCreate(assignments, {
      ignoreDuplicates: true,
      returning: true
    });

    // Update existing assignments to active status
    await MOOrgModule.update(
      { 
        status: 'active',
        updated_by: req.user.id,
        updatedAt: new Date()
      },
      {
        where: {
          module_id: { [Op.in]: module_ids },
          organization_id: { [Op.in]: organization_ids.map(id => id.toString()) }
        }
      }
    );

    // Reactivate permissions for assigned modules
    await reactivateModulePermissions(module_ids, organization_ids, req.user.id);

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Modules assigned to organizations successfully",
      data: {
        assignments_created: createdAssignments.length,
        modules: modules,
        organizations: organization_ids
      }
    });

  } catch (error) {
    console.error("assignModulesToOrganizations error:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Internal server error"
    });
  }
};

/**
 * Assign widgets to organizations
 * @param req - Request object containing widget_ids and organization_ids
 * @param res - Response object
 */
export const assignWidgetsToOrganizations = async (req: Request, res: Response) => {
  try {
    const { widget_ids, organization_ids } = req.body;

    // Validate required fields
    if (!widget_ids || !Array.isArray(widget_ids) || widget_ids.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "widget_ids array is required and cannot be empty"
      });
    }

    if (!organization_ids || !Array.isArray(organization_ids) || organization_ids.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "organization_ids array is required and cannot be empty"
      });
    }

    // Only super admins (organization_id = null) can assign widgets to organizations
    if (req.user.organization_id !== null) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Only super admins can assign widgets to organizations"
      });
    }

    // Validate that all widgets exist
    const widgets = await MOWidget.findAll({
      where: { id: { [Op.in]: widget_ids } },
      attributes: ['id', 'name', 'widget', 'slug']
    });

    if (widgets.length !== widget_ids.length) {
      const foundWidgetIds = widgets.map(w => w.id);
      const missingWidgetIds = widget_ids.filter(id => !foundWidgetIds.includes(id));
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: `Widgets not found: ${missingWidgetIds.join(', ')}`
      });
    }

    // Validate that all organizations exist by checking users with those organization_ids
    const organizationUsers = await User.findAll({
      where: {
        organization_id: { [Op.in]: organization_ids.map(id => id.toString()) }
      },
      attributes: ['organization_id'],
      group: ['organization_id'],
      raw: true
    });

    const foundOrgIds = organizationUsers.map(u => u.organization_id);
    const missingOrgIds = organization_ids.filter(id => !foundOrgIds.includes(id.toString()));

    if (missingOrgIds.length > 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: `Organizations not found: ${missingOrgIds.join(', ')}`
      });
    }

    // Create assignments
    const assignments: any[] = [];
    for (const widget_id of widget_ids) {
      for (const organization_id of organization_ids) {
        assignments.push({
          widget_id,
          organization_id: organization_id.toString(),
          status: 'active',
          created_by: req.user.id,
          updated_by: req.user.id
        });
      }
    }

    // Bulk create assignments (will ignore duplicates due to unique constraint)
    const createdAssignments = await MOOrgWidget.bulkCreate(assignments, {
      ignoreDuplicates: true,
      returning: true
    });

    // Update existing assignments to active status
    await MOOrgWidget.update(
      { 
        status: 'active',
        updated_by: req.user.id,
        updatedAt: new Date()
      },
      {
        where: {
          widget_id: { [Op.in]: widget_ids },
          organization_id: { [Op.in]: organization_ids.map(id => id.toString()) }
        }
      }
    );

    // Reactivate permissions for assigned widgets
    await reactivateWidgetPermissions(widget_ids, organization_ids, req.user.id);

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Widgets assigned to organizations successfully",
      data: {
        assignments_created: createdAssignments.length,
        widgets: widgets,
        organizations: organization_ids
      }
    });

  } catch (error) {
    console.error("assignWidgetsToOrganizations error:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Internal server error"
    });
  }
};

/**
 * Get modules assigned to an organization
 * @param req - Request object
 * @param res - Response object
 */
export const getOrganizationModules = async (req: Request, res: Response) => {
  try {
    const { organization_id } = req.params;

    if (!organization_id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "organization_id is required"
      });
    }

    // Only super admins (organization_id = null) can view organization module assignments
    if (req.user.organization_id !== null) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Only super admins can view organization module assignments"
      });
    }

    const assignedModules = await MOOrgModule.findAll({
      where: {
        organization_id: organization_id.toString(),
        status: 'active'
      },
      include: [{
        model: MOModule,
        as: 'module',
        attributes: ['id', 'module', 'module_name', 'index']
      }],
      order: [['module', 'index', 'ASC']]
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Organization modules retrieved successfully",
      data: assignedModules
    });

  } catch (error) {
    console.error("getOrganizationModules error:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Internal server error"
    });
  }
};

/**
 * Get widgets assigned to an organization
 * @param req - Request object
 * @param res - Response object
 */
export const getOrganizationWidgets = async (req: Request, res: Response) => {
  try {
    const { organization_id } = req.params;

    if (!organization_id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "organization_id is required"
      });
    }

    // Only super admins (organization_id = null) can view organization widget assignments
    if (req.user.organization_id !== null) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Only super admins can view organization widget assignments"
      });
    }

    const assignedWidgets = await MOOrgWidget.findAll({
      where: {
        organization_id: organization_id.toString(),
        status: 'active'
      },
      include: [{
        model: MOWidget,
        as: 'widget',
        attributes: ['id', 'name', 'widget', 'type', 'subType', 'slug', 'widget_category']
      }],
      order: [['widget', 'index', 'ASC']]
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Organization widgets retrieved successfully",
      data: assignedWidgets
    });

  } catch (error) {
    console.error("getOrganizationWidgets error:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Internal server error"
    });
  }
};

/**
 * Remove module assignments from organizations
 * @param req - Request object
 * @param res - Response object
 */
export const removeModuleAssignments = async (req: Request, res: Response) => {
  try {
    const { module_ids, organization_ids } = req.body;

    // Validate required fields
    if (!module_ids || !Array.isArray(module_ids) || module_ids.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "module_ids array is required and cannot be empty"
      });
    }

    if (!organization_ids || !Array.isArray(organization_ids) || organization_ids.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "organization_ids array is required and cannot be empty"
      });
    }

    // Only super admins (organization_id = null) can remove module assignments
    if (req.user.organization_id !== null) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Only super admins can remove module assignments"
      });
    }

    // Deactivate permissions for removed modules
    await deactivateModulePermissions(module_ids, organization_ids, req.user.id);

    // Update assignments to deleted status
    const [updatedCount] = await MOOrgModule.update(
      {
        status: 'deleted',
        updated_by: req.user.id
      },
      {
        where: {
          module_id: { [Op.in]: module_ids },
          organization_id: { [Op.in]: organization_ids.map(id => id.toString()) }
        }
      }
    );

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Module assignments removed successfully",
      data: {
        assignments_removed: updatedCount
      }
    });

  } catch (error) {
    console.error("removeModuleAssignments error:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Internal server error"
    });
  }
};

/**
 * Remove widget assignments from organizations
 * @param req - Request object
 * @param res - Response object
 */
export const removeWidgetAssignments = async (req: Request, res: Response) => {
  try {
    const { widget_ids, organization_ids } = req.body;

    // Validate required fields
    if (!widget_ids || !Array.isArray(widget_ids) || widget_ids.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "widget_ids array is required and cannot be empty"
      });
    }

    if (!organization_ids || !Array.isArray(organization_ids) || organization_ids.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "organization_ids array is required and cannot be empty"
      });
    }

    // Only super admins (organization_id = null) can remove widget assignments
    if (req.user.organization_id !== null) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Only super admins can remove widget assignments"
      });
    }

    // Deactivate permissions for removed widgets
    await deactivateWidgetPermissions(widget_ids, organization_ids, req.user.id);

    // Update assignments to deleted status
    const [updatedCount] = await MOOrgWidget.update(
      {
        status: 'deleted',
        updated_by: req.user.id
      },
      {
        where: {
          widget_id: { [Op.in]: widget_ids },
          organization_id: { [Op.in]: organization_ids.map(id => id.toString()) }
        }
      }
    );

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Widget assignments removed successfully",
      data: {
        assignments_removed: updatedCount
      }
    });

  } catch (error) {
    console.error("removeWidgetAssignments error:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Internal server error"
    });
  }
};

/**
 * Reactivate module permissions when modules are assigned to organizations
 * @param module_ids - Array of module IDs
 * @param organization_ids - Array of organization IDs
 * @param user_id - User ID for audit trail
 */
export const reactivateModulePermissions = async (
  module_ids: number[],
  organization_ids: (string | number)[],
  user_id: number
) => {
  try {
    console.log(`🔄 Reactivating module permissions for modules: ${module_ids.join(', ')} in organizations: ${organization_ids.join(', ')}`);

    // Update permissions status from inactive to active for assigned modules
    const [updatedCount] = await MOPermission.update(
      {
        status: permission_status.ACTIVE,
        updated_by: user_id
      },
      {
        where: {
          module_id: { [Op.in]: module_ids },
          organization_id: { [Op.in]: organization_ids.map(id => id.toString()) },
          status: permission_status.INACTIVE
        }
      }
    );

    console.log(`✅ Reactivated ${updatedCount} module permissions`);
    return updatedCount;
  } catch (error) {
    console.error('Error reactivating module permissions:', error);
    throw error;
  }
};

/**
 * Reactivate widget permissions when widgets are assigned to organizations
 * @param widget_ids - Array of widget IDs
 * @param organization_ids - Array of organization IDs
 * @param user_id - User ID for audit trail
 */
export const reactivateWidgetPermissions = async (
  widget_ids: number[],
  organization_ids: (string | number)[],
  user_id: number
) => {
  try {
    console.log(`🔄 Reactivating widget permissions for widgets: ${widget_ids.join(', ')} in organizations: ${organization_ids.join(', ')}`);

    // Update permissions status from inactive to active for assigned widgets
    const [updatedCount] = await MOPermission.update(
      {
        status: permission_status.ACTIVE,
        updated_by: user_id
      },
      {
        where: {
          widget_id: { [Op.in]: widget_ids },
          organization_id: { [Op.in]: organization_ids.map(id => id.toString()) },
          status: permission_status.INACTIVE
        }
      }
    );

    console.log(`✅ Reactivated ${updatedCount} widget permissions`);
    return updatedCount;
  } catch (error) {
    console.error('Error reactivating widget permissions:', error);
    throw error;
  }
};

/**
 * Deactivate module permissions when modules are removed from organizations
 * @param module_ids - Array of module IDs
 * @param organization_ids - Array of organization IDs
 * @param user_id - User ID for audit trail
 */
export const deactivateModulePermissions = async (
  module_ids: number[],
  organization_ids: (string | number)[],
  user_id: number
) => {
  try {
    console.log(`🔄 Deactivating module permissions for modules: ${module_ids.join(', ')} in organizations: ${organization_ids.join(', ')}`);

    // Update permissions status from active to inactive for removed modules
    const [updatedCount] = await MOPermission.update(
      {
        status: permission_status.INACTIVE,
        updated_by: user_id
      },
      {
        where: {
          module_id: { [Op.in]: module_ids },
          organization_id: { [Op.in]: organization_ids.map(id => id.toString()) },
          status: permission_status.ACTIVE
        }
      }
    );

    console.log(`✅ Deactivated ${updatedCount} module permissions`);
    return updatedCount;
  } catch (error) {
    console.error('Error deactivating module permissions:', error);
    throw error;
  }
};

/**
 * Deactivate widget permissions when widgets are removed from organizations
 * @param widget_ids - Array of widget IDs
 * @param organization_ids - Array of organization IDs
 * @param user_id - User ID for audit trail
 */
export const deactivateWidgetPermissions = async (
  widget_ids: number[],
  organization_ids: (string | number)[],
  user_id: number
) => {
  try {
    console.log(`🔄 Deactivating widget permissions for widgets: ${widget_ids.join(', ')} in organizations: ${organization_ids.join(', ')}`);

    // Update permissions status from active to inactive for removed widgets
    const [updatedCount] = await MOPermission.update(
      {
        status: permission_status.INACTIVE,
        updated_by: user_id
      },
      {
        where: {
          widget_id: { [Op.in]: widget_ids },
          organization_id: { [Op.in]: organization_ids.map(id => id.toString()) },
          status: permission_status.ACTIVE
        }
      }
    );

    console.log(`✅ Deactivated ${updatedCount} widget permissions`);
    return updatedCount;
  } catch (error) {
    console.error('Error deactivating widget permissions:', error);
    throw error;
  }
};
