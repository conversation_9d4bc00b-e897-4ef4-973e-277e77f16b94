-- Remove unique constraint on widget slug per organization
-- This allows template widgets (organization_id = NULL) to be copied to multiple organizations

-- Check if the constraint exists before dropping it
SELECT CONSTRAINT_NAME, TABLE_NAME, CONSTRAINT_TYPE 
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
WHERE TABLE_NAME = 'mo_widgets' 
AND CONSTRAINT_NAME = 'unique_widget_slug_per_org';

-- Drop the unique constraint if it exists
ALTER TABLE mo_widgets DROP INDEX IF EXISTS unique_widget_slug_per_org;

-- Verify the constraint has been removed
SELECT CONSTRAINT_NAME, TABLE_NAME, CONSTRAINT_TYPE 
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
WHERE TABLE_NAME = 'mo_widgets' 
AND CONSTRAINT_NAME = 'unique_widget_slug_per_org';

-- Show current indexes on mo_widgets table
SHOW INDEX FROM mo_widgets;
