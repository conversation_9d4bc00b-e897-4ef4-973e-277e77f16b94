{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "Roles, Permissions & Modules API", "description": "Complete API collection for managing roles, permissions, and modules using MORole, MOPermission, and MOModule models", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Roles Management", "item": [{"name": "Create Role", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"role_name\": \"Branch Manager\",\n  \"platform\": 1,\n  \"parent_role_id\": 1\n}"}, "url": {"raw": "{{base_url}}/v1/private/role/create", "host": ["{{base_url}}"], "path": ["v1", "private", "role", "create"]}}}, {"name": "Get Roles List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/role/list?page=1&size=10&search=manager&status=active", "host": ["{{base_url}}"], "path": ["v1", "private", "role", "list"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}, {"key": "search", "value": "manager"}, {"key": "status", "value": "active"}]}}}, {"name": "Update Role", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"role_name\": \"Senior Branch Manager\",\n  \"platform\": 1,\n  \"parent_role_id\": 1\n}"}, "url": {"raw": "{{base_url}}/v1/private/role/update/{{role_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "role", "update", "{{role_id}}"]}}}, {"name": "Delete Role", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/role/delete/{{role_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "role", "delete", "{{role_id}}"]}}}]}, {"name": "Permissions Management", "item": [{"name": "Create Permission", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"role_id\": 1,\n  \"module_id\": 1,\n  \"permission\": 7,\n  \"platform\": 1,\n  \"partial\": false\n}"}, "url": {"raw": "{{base_url}}/v1/private/role/permissions", "host": ["{{base_url}}"], "path": ["v1", "private", "role", "permissions"]}}}, {"name": "Get Permissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/role/permissions?role_id=1&platform=1&search=user", "host": ["{{base_url}}"], "path": ["v1", "private", "role", "permissions"], "query": [{"key": "role_id", "value": "1"}, {"key": "platform", "value": "1"}, {"key": "search", "value": "user"}]}}}, {"name": "Get Permission by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/role/permissions/{{permission_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "role", "permissions", "{{permission_id}}"]}}}, {"name": "Update Permissions", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"role_id\": 1,\n  \"platform\": 1,\n  \"permissions\": {\n    \"1\": {\n      \"permission\": 15,\n      \"partial\": false\n    },\n    \"2\": {\n      \"permission\": 7,\n      \"partial\": true\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/v1/private/role/permissions", "host": ["{{base_url}}"], "path": ["v1", "private", "role", "permissions"]}}}, {"name": "Delete Permission", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/role/permissions/{{permission_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "role", "permissions", "{{permission_id}}"]}}}, {"name": "Copy Permissions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"from_role\": 1,\n  \"to_role\": 2\n}"}, "url": {"raw": "{{base_url}}/v1/private/role/permissions/copy", "host": ["{{base_url}}"], "path": ["v1", "private", "role", "permissions", "copy"]}}}]}, {"name": "Modules Management", "item": [{"name": "Create Mo<PERSON>le", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"module\": \"inventory\",\n  \"module_name\": \"Inventory Management\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/role/modules", "host": ["{{base_url}}"], "path": ["v1", "private", "role", "modules"]}}}, {"name": "Get Modules List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/role/modules?page=1&size=10&search=user&id=1", "host": ["{{base_url}}"], "path": ["v1", "private", "role", "modules"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}, {"key": "search", "value": "user"}, {"key": "id", "value": "1"}]}}}, {"name": "Update Module", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"module\": \"inventory_management\",\n  \"module_name\": \"Advanced Inventory Management\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/role/modules/{{module_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "role", "modules", "{{module_id}}"]}}}, {"name": "Delete Module", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/v1/private/role/modules/{{module_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "role", "modules", "{{module_id}}"]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:3000"}, {"key": "auth_token", "value": "your_jwt_token_here"}, {"key": "role_id", "value": "1"}, {"key": "permission_id", "value": "1"}, {"key": "module_id", "value": "1"}]}