{"name": "server", "version": "1.0.0", "description": "", "main": "src/index.ts", "scripts": {"build": "npx tsc -p . && cp -R src/email_templates build/email_templates", "lint": "eslint --ignore-path .eslint<PERSON>ore --ext .js,.ts .", "format": "prettier --ignore-path .gitignore --write \"**/*.+(js|ts|json)\"", "dev": "ts-node --files ./src/index.ts", "production": "node ./build/index.js", "seeder:all": "npx sequelize-cli db:seed:all", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:validatePermission": "jest validateModulePermission.test.ts"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^20.12.2", "@types/node-cron": "^3.0.11", "@typescript-eslint/eslint-plugin": "^7.4.0", "@typescript-eslint/parser": "^7.4.0", "eslint": "^8.57.0", "jest": "^29.7.0", "prettier": "^3.2.5", "swagger-autogen": "^2.23.7", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.4.3"}, "dependencies": {"@aws-sdk/client-s3": "^3.816.0", "amqplib": "^0.10.8", "better-queue": "^3.8.12", "body-parser": "^1.20.2", "celebrate": "^15.0.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crc": "^4.3.2", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "express": "^4.19.2", "fs-extra": "^11.2.0", "handlebars": "^4.7.8", "html-pdf-node": "^1.0.8", "http-status-codes": "^2.3.0", "i18n": "^0.15.1", "json-2-csv": "^5.5.6", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lodash": "^4.17.21", "mmmagic": "^0.5.3", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "mysql2": "^3.9.3", "node-cron": "^3.0.3", "nodemailer": "^6.9.13", "onesignal-node": "^3.4.0", "pbkdf2-password-hash": "^3.1.1", "pdf-lib": "^1.17.1", "puppeteer": "^22.11.2", "sequelize": "^6.37.2", "sequelize-cli": "^6.6.2", "server": "file:", "sharp": "^0.33.4", "swagger-ui-express": "^5.0.1", "ua-parser-js": "^1.0.38", "xlsx": "^0.18.5"}}