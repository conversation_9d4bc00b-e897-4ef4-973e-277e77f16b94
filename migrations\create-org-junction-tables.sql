-- Create junction tables for organization-module and organization-widget relationships

-- Create mo_org_modules table
CREATE TABLE IF NOT EXISTS `mo_org_modules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `module_id` int(11) NOT NULL,
  `organization_id` varchar(255) NOT NULL,
  `status` enum('active','deleted') NOT NULL DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_module_org` (`module_id`, `organization_id`),
  KEY `idx_organization_id` (`organization_id`),
  KEY `idx_module_id` (`module_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_mo_org_modules_module` FOREIGN KEY (`module_id`) REFERENCES `mo_modules` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_mo_org_modules_organization` FOREIGN KEY (`organization_id`) REFERENCES `nv_organizations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create mo_org_widgets table
CREATE TABLE IF NOT EXISTS `mo_org_widgets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `widget_id` int(11) NOT NULL,
  `organization_id` varchar(255) NOT NULL,
  `status` enum('active','deleted') NOT NULL DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_widget_org` (`widget_id`, `organization_id`),
  KEY `idx_organization_id` (`organization_id`),
  KEY `idx_widget_id` (`widget_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_mo_org_widgets_widget` FOREIGN KEY (`widget_id`) REFERENCES `mo_widgets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_mo_org_widgets_organization` FOREIGN KEY (`organization_id`) REFERENCES `nv_organizations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Migrate existing data from mo_modules to mo_org_modules
INSERT INTO mo_org_modules (module_id, organization_id, status, created_by, updated_by, createdAt, updatedAt)
SELECT 
  m.id as module_id,
  m.organization_id,
  'active' as status,
  m.created_by,
  m.updated_by,
  m.createdAt,
  m.updatedAt
FROM mo_modules m
WHERE m.organization_id IS NOT NULL
ON DUPLICATE KEY UPDATE
  status = 'active',
  updated_by = VALUES(updated_by),
  updatedAt = VALUES(updatedAt);

-- Migrate existing data from mo_widgets to mo_org_widgets
INSERT INTO mo_org_widgets (widget_id, organization_id, status, created_by, updated_by, createdAt, updatedAt)
SELECT 
  w.id as widget_id,
  w.organization_id,
  'active' as status,
  w.created_by,
  w.updated_by,
  w.createdAt,
  w.updatedAt
FROM mo_widgets w
WHERE w.organization_id IS NOT NULL
ON DUPLICATE KEY UPDATE
  status = 'active',
  updated_by = VALUES(updated_by),
  updatedAt = VALUES(updatedAt);

-- Remove organization_id from mo_modules (keep global modules only)
-- First, identify and keep only one global copy of each module
CREATE TEMPORARY TABLE temp_global_modules AS
SELECT 
  MIN(id) as keep_id,
  module,
  module_name,
  MIN(created_by) as created_by,
  MIN(updated_by) as updated_by,
  MIN(createdAt) as createdAt,
  MAX(updatedAt) as updatedAt
FROM mo_modules
GROUP BY module, module_name;

-- Delete all modules except the ones we want to keep
DELETE FROM mo_modules 
WHERE id NOT IN (SELECT keep_id FROM temp_global_modules);

-- Update the kept modules to remove organization_id
UPDATE mo_modules SET organization_id = NULL WHERE organization_id IS NOT NULL;

-- Remove organization_id from mo_widgets (keep global widgets only)
-- First, identify and keep only one global copy of each widget
CREATE TEMPORARY TABLE temp_global_widgets AS
SELECT 
  MIN(id) as keep_id,
  `index`,
  name,
  widget,
  type,
  subType,
  filters,
  slug,
  call_api,
  widget_category,
  MIN(created_by) as created_by,
  MIN(updated_by) as updated_by,
  MIN(createdAt) as createdAt,
  MAX(updatedAt) as updatedAt
FROM mo_widgets
GROUP BY `index`, name, widget, type, subType, filters, slug, call_api, widget_category;

-- Delete all widgets except the ones we want to keep
DELETE FROM mo_widgets 
WHERE id NOT IN (SELECT keep_id FROM temp_global_widgets);

-- Update the kept widgets to remove organization_id
UPDATE mo_widgets SET organization_id = NULL WHERE organization_id IS NOT NULL;

-- Clean up temporary tables
DROP TEMPORARY TABLE temp_global_modules;
DROP TEMPORARY TABLE temp_global_widgets;
