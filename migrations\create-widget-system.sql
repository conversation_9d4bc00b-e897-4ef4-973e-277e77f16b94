-- Migration: Create Widget System
-- Description: Creates mo_widgets table and updates mo_permissions table for widget support
-- Date: 2025-01-17

-- Create mo_widgets table
CREATE TABLE IF NOT EXISTS `mo_widgets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `widget` text NOT NULL,
  `type` enum('chart','number','percentage','table','list','card','metric') NOT NULL,
  `subType` enum('pie','line','bar','donut','area','column','gauge','counter','progress','simple_table','data_table','bullet_list','numbered_list','info_card','stat_card','kpi','summary') NOT NULL,
  `filters` json DEFAULT NULL,
  `slug` varchar(100) NOT NULL,
  `organization_id` varchar(255) NOT NULL,
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) NOT NULL,
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_widget_slug_per_org` (`slug`,`organization_id`),
  KEY `idx_widget_organization` (`organization_id`),
  KEY `idx_widget_type` (`type`),
  KEY `idx_widget_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add widget_id and order columns to mo_permissions table
ALTER TABLE `mo_permissions` 
ADD COLUMN `widget_id` int(11) DEFAULT NULL AFTER `module_id`,
ADD COLUMN `order` int(11) NOT NULL DEFAULT 0 AFTER `status`;

-- Add foreign key constraint for widget_id
ALTER TABLE `mo_permissions` 
ADD CONSTRAINT `fk_mo_permissions_widget_id` 
FOREIGN KEY (`widget_id`) REFERENCES `mo_widgets` (`id`) ON DELETE CASCADE;

-- Add index for widget_id
ALTER TABLE `mo_permissions` 
ADD INDEX `idx_mo_permissions_widget_id` (`widget_id`);

-- Remove type and order columns from mo_modules table (if they exist)
-- Note: Be careful with this in production - ensure no data depends on these fields
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_NAME = 'mo_modules' 
     AND COLUMN_NAME = 'type' 
     AND TABLE_SCHEMA = DATABASE()) > 0,
    'ALTER TABLE mo_modules DROP COLUMN type',
    'SELECT "Column type does not exist in mo_modules" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_NAME = 'mo_modules' 
     AND COLUMN_NAME = 'order' 
     AND TABLE_SCHEMA = DATABASE()) > 0,
    'ALTER TABLE mo_modules DROP COLUMN `order`',
    'SELECT "Column order does not exist in mo_modules" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Insert sample widget module into mo_modules table as GLOBAL module
INSERT IGNORE INTO `mo_modules` (`module`, `module_name`, `organization_id`, `created_by`, `updated_by`, `createdAt`, `updatedAt`)
VALUES
('widget', 'Widget Management', NULL, 1, 1, NOW(), NOW());

-- Insert sample widgets as GLOBAL widgets
INSERT IGNORE INTO `mo_widgets` (`name`, `widget`, `type`, `subType`, `filters`, `slug`, `organization_id`, `created_by`, `updated_by`, `createdAt`, `updatedAt`)
VALUES
('User Statistics', '{"title": "User Statistics", "data": "user_count", "config": {"showTotal": true}}', 'number', 'counter', '[{"label": "Current Month", "value": "month"}, {"label": "Current Year", "value": "year"}]', 'user_statistics', NULL, 1, 1, NOW(), NOW()),
('Department Distribution', '{"title": "Department Distribution", "data": "department_stats", "config": {"showLabels": true}}', 'chart', 'pie', '[{"label": "All Departments", "value": "all"}, {"label": "Active Only", "value": "active"}]', 'department_distribution', NULL, 1, 1, NOW(), NOW()),
('Monthly Performance', '{"title": "Monthly Performance", "data": "performance_metrics", "config": {"showTrend": true}}', 'chart', 'line', '[{"label": "Last 6 Months", "value": "6m"}, {"label": "Last 12 Months", "value": "12m"}]', 'monthly_performance', NULL, 1, 1, NOW(), NOW()),
('Task Progress', '{"title": "Task Progress", "data": "task_completion", "config": {"showPercentage": true}}', 'percentage', 'progress', '[{"label": "Today", "value": "today"}, {"label": "This Week", "value": "week"}]', 'task_progress', NULL, 1, 1, NOW(), NOW()),
('Recent Activities', '{"title": "Recent Activities", "data": "activity_log", "config": {"limit": 10}}', 'list', 'bullet_list', '[{"label": "Last 24 Hours", "value": "24h"}, {"label": "Last 7 Days", "value": "7d"}]', 'recent_activities', NULL, 1, 1, NOW(), NOW());

-- NOTE: Widget permissions should be created through the proper role/permission management system
-- This migration only creates the global widget module and widgets
-- Organizations will assign widgets through the junction table system
-- Permissions will be created when roles are assigned widget access

-- Assign widget module to all existing organizations through junction table
INSERT IGNORE INTO `mo_org_modules` (`module_id`, `organization_id`, `status`, `created_by`, `updated_by`, `createdAt`, `updatedAt`)
SELECT
    m.id as module_id,
    o.id as organization_id,
    'active' as status,
    1 as created_by,
    1 as updated_by,
    NOW() as createdAt,
    NOW() as updatedAt
FROM mo_modules m
CROSS JOIN nv_organizations o
WHERE m.module = 'widget'
AND m.organization_id IS NULL
AND NOT EXISTS (
    SELECT 1 FROM mo_org_modules om
    WHERE om.module_id = m.id
    AND om.organization_id = o.id
);

-- Assign sample widgets to all existing organizations through junction table
INSERT IGNORE INTO `mo_org_widgets` (`widget_id`, `organization_id`, `status`, `created_by`, `updated_by`, `createdAt`, `updatedAt`)
SELECT
    w.id as widget_id,
    o.id as organization_id,
    'active' as status,
    1 as created_by,
    1 as updated_by,
    NOW() as createdAt,
    NOW() as updatedAt
FROM mo_widgets w
CROSS JOIN nv_organizations o
WHERE w.organization_id IS NULL
AND NOT EXISTS (
    SELECT 1 FROM mo_org_widgets ow
    WHERE ow.widget_id = w.id
    AND ow.organization_id = o.id
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_mo_permissions_role_widget` ON `mo_permissions` (`role_id`, `widget_id`);
CREATE INDEX IF NOT EXISTS `idx_mo_permissions_organization_widget` ON `mo_permissions` (`organization_id`, `widget_id`);
CREATE INDEX IF NOT EXISTS `idx_mo_permissions_order` ON `mo_permissions` (`order`);

-- Migration completed successfully
SELECT 'Widget system migration completed successfully!' as message;
